<?php

namespace App\Models;

use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductVariantQtyRange extends Model
{
    use HasFactory;
    protected $table = 'product_variant_qty_range';
    public $timestamps = false;


    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'id', 'id');
    }

    // public function children()
    // {
    //     return $this->hasMany(ProductVariantQtyRange::class);
    // }
}

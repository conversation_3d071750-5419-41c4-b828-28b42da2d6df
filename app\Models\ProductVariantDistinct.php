<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductVariantDistinct extends Model
{
    use HasFactory;
    protected $table = 'product_variant_distinct';

    public function discountRanges()
    {
        return $this->hasMany(ProductVariantQtyRange::class, 'product_id', 'product_id');
    }

    public function product()
    {

        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function variantDiscounts()
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'product_id');
    }

    // public function getName()
    // {
    //     return        $this->name;
    // }
}

<?php

namespace App\Models;

use App\Models\GraphicBrandAttchments;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class GraphicLeadBrand extends Model
{
    use HasFactory;

    protected $table = 'graphic_lead_brand';
    public $timestamps = false;

    public function attachments(){
        return $this->hasMany(GraphicBrandAttchments::class,'graphic_brand_id','id');
    }
}

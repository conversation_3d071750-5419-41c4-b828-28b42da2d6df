<?php

use App\Models\User;
use Aws\S3\S3Client;
use App\Models\Product;
use App\Models\EmailOtpProvider;
use App\Models\Lead;
use App\Models\ProductCategory;
use AWS\S3\Exception\S3Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PHPMailer\PHPMailer\PHPMailer;

if (!function_exists('hostData')) {
    function hostData()
    {
        $host = request()->getHttpHost();

        // Cache host data to reduce database queries
        return Cache::remember("host_data_{$host}", now()->addHours(24), function () use ($host) {
            return DB::table('login_host')->where('hostname', $host)->first();
        });
    }
}

if (!function_exists('CategoryDetails')) {
    function CategoryDetails($name = null)
    {
        // DB::enableQueryLog();
        if ($name == null) {

            $cateData = ProductCategory::query()->where('isActive', 1)->with('products')->get();
        } else {
            $cateData = ProductCategory::query()->where('isActive', 1)->where('name', $name)->with('products')->first();
        }
        // dd($cateData);
        return $cateData;
    }
}

/**
 * @return S3Client
 */

if (!function_exists('s3_con')) {
    function s3_con()
    {
        // Cache S3 client to avoid recreating for each request
        static $s3Client = null;

        if ($s3Client !== null) {
            return $s3Client;
        }

        $hostData = hostData();
        if (
            !empty($hostData->aws_default_region) &&
            !empty($hostData->aws_secret_access_key) &&
            !empty($hostData->aws_access_key_id)
        ) {

            $s3Client = new S3Client([
                "version" => "latest",
                "region" => $hostData->aws_default_region,
                "http" => ["verify" => false],
                'credentials' => [
                    'key'    => $hostData->aws_access_key_id,
                    'secret' => $hostData->aws_secret_access_key,
                ]
            ]);

            return $s3Client;
        }

        return false;
    }
}
if (!function_exists('s3_fileUpload')) {
    function s3_fileUpload($image, $table_name, bool $access_control = false): string
    {

        $hostData = hostData();
        $ACL = $access_control  ? "public" : "private";

        $file_name = time() . "_" . $image->getClientOriginalName();
        $s3 = s3_con();
        if ($s3) {
            try {
                $res = $s3->putObject([
                    "Bucket" =>  $hostData->aws_bucket,
                    "Key" =>  $hostData->adminHostname . '/' . $table_name . '/' . $file_name,
                    'ACL' => $ACL,
                    'SourceFile' => $image,
                ]);
                $result_arr = $res->toArray();
                if (!empty($result_arr["ObjectURL"])) {
                    return $file_name;
                } else {
                    return "file upload failed";
                }
            } catch (S3Exception) {

                return "file upload failed";
            }
        } else {
            return false;
        }
    }
}
if (!function_exists('s3_fileDelete')) {
    function s3_fileDelete($image, $table_name): string
    {
        $hostData = hostData();
        // deleting the uploaded image
        $delkey =  $hostData->adminHostname . "/" . "$table_name" . "/$image";
        $s3 = s3_con();
        try {
            $s3->deleteObject([
                'Bucket' =>  $hostData->aws_bucket,
                'Key' => $delkey,
            ]);
            return "object deleted successfully";
        } catch (S3Exception $e) {
            return $e->getMessage();
        }
    }
}
if (!function_exists('s3_fileShow')) {
    function s3_fileShow($image_name, $table_name, $product_name = null)
    {
        // Cache S3 URLs to reduce API calls
        $cacheKey = "s3_url_{$table_name}_{$image_name}";

        return Cache::remember($cacheKey, now()->addHours(12), function () use ($image_name, $table_name, $product_name) {
            try {
                $hostData = hostData();
                $filename = $hostData->adminHostname . "/" . "$table_name" . "/$image_name";
                $s3 = s3_con();

                if (!$s3) {
                    throw new Exception("S3 connection failed");
                }

                $fileExist = $s3->doesObjectExist($hostData->aws_bucket, $filename);
                if ($fileExist) {
                    $command = $s3->getCommand(
                        'GetObject',
                        [
                            'Bucket' => $hostData->aws_bucket,
                            'Key' => $filename,
                        ]
                    );
                    $request = $s3->createPresignedRequest($command, "+1 days");
                    return (string)$request->getUri();
                } else {
                    if ($product_name) {
                        return "https://dummyimage.com/500/969696/ffffff.jpg&text=$product_name";
                    } else {
                        return "https://dummyimage.com/500/969696/ffffff.jpg&text=$image_name";
                    }
                }
            } catch (Exception $e) {
                Log::error("S3 file show error: " . $e->getMessage());
                return false;
            }
        });
    }
}

if (!function_exists('humanDate')) {
    function humanDate($date, $format = null): string
    {
        return date($format ?? env('DATE_FORMATE'), strtotime($date));
    }
}
if (!function_exists('products')) {
    function products()
    {
        return Product::where('isActive', 1)->where('isOnWebsite', 1)->get();
    }
}
if (!function_exists('ErrMsg')) {

    function ErrMsg(): string
    {
        return 'Due to some system error we are not able to process with your request currently. Please try again later.';
    }
}

if (!function_exists('allDomains')) {
    function allDomains($domain = null): array
    {
        $cacheKey = 'all_domains_data';
        $cacheDuration = 3600; // 1 hour in seconds

        $result = Cache::remember($cacheKey, $cacheDuration, function () {
            $domains = [
                'wabhai' => ['localhost:8001', 'wabhai.com', 'wabhai.orkia.in'],
                'primailer' => ['localhost:8002', 'primailer.com', 'primailer.orkia.in'],
                'stickyfirst' => ['localhost:8003', 'stickyfirst.com', 'stickyfirst.orkia.in'],
                'ringcaster' => ['localhost:8004', 'ringcaster.com', 'ringcaster.orkia.in'],
                'pixayogi' => ['localhost:8005', 'pixayogi.com', 'pixayogi.orkia.in'],
                'rokdi' => ['localhost:8006', 'rokdi.com', 'rokdi.orkia.in'],
                'androsms' => ['localhost:8007', 'androsms.com', 'androsms.orkia.in'],
                'clatos' => ['localhost:8008', 'clatos.com', 'clatos.orkia.in'],
                'rapbooster' => ['localhost:8009', 'rapbooster.com', 'rapbooster.orkia.in'],
                'dunesfactory' => ['localhost:8010', 'dunesfactory.com', 'dunesfactory.orkia.in'],
                'andro' => ['localhost:8011', 'andro.com', 'andro.orkia.in'],
                'texaplus' => ['localhost:8019', 'texaplus.com', 'texaplus.orkia.in'],
            ];

            $domainData = [
                'wabhai' => [
                    'icon' => 'assets/global/images/wb2.png',
                    "name" => 'WaBhai',
                    'logo' => 'media/logo/wabhai-full.png',
                    'main_path' => 'components.wabhai.main',
                    'product_id' => 81,
                    'btnColor' => ['normal' => '#2c5956', 'disable' => '#153230', 'hover' => '#153230'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#27CC76',
                            'hover' => '#00ac52',
                            'focus' => '#00ac52',
                            'disable' => '#00833f',
                            'text' => ["black" => '#000000', "white" => "#ffffff"],
                            'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                            'font-color' => 'secondary'
                        ],
                        "sectionBg" => "#e8fcf3",
                        "sectionBgAlternate" => "#ffffff",
                    ]
                ],
                'primailer' => [
                    'icon' => 'assets/global/images/pm.png',
                    'logo' => asset('media/logo/primailer-full.png'),
                    "name" => 'PriMailer',
                    'main_path' => 'components.primailer.main',
                    'product_id' => 37,
                    'btnColor' => ['normal' => '#4489d5', 'disable' => '#4489d5', 'hover' => '#2976c3'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => ['main' => '#286FB4', 'hover' => '#0055a8', 'focus' => '#0055a8', 'disable' => '#496987', 'text' => ["black" => '#000000', "white" => "#ffffff"]],
                        "sectionBg" => "#e0efff",
                        "sectionBgAlternate" => "#ffffff",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'stickyfirst' => [
                    'icon' => 'assets/global/images/sf.png',
                    "name" => 'StickyFirst',
                    'logo' => asset('media/logo/stickyfirst-full.png'),
                    'main_path' => 'components.stickyfirst.main',
                    'product_id' => 48,
                    'btnColor' => ['normal' => '#212529', 'disable' => '#212529', 'hover' => '#212529'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => ['main' => '#27CC76', 'hover' => '#00ac52', 'focus' => '#00ac52', 'disable' => '#00833f', 'text' => ["black" => '#000000', "white" => "#ffffff"]],
                        "sectionBg" => "#ffffff",
                        "sectionBgAlternate" => "#ffffff",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'ringcaster' => [
                    'logo' => asset('media/logo/ringcaster-full.png'),
                    'icon' => 'assets/global/images/rc.png',
                    "name" => 'RingCaster',
                    'main_path' => 'components.ringcaster.main',
                    'product_id' => 42,
                    'btnColor' => ['normal' => '#f5893a', 'disable' => '#f5893a', 'hover' => '#f36f21'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#F58B3E',
                            'hover' => '#F37224',
                            'focus' => '#F37224',
                            'disable' => '#b78159',
                            'text' => ["black" => '#000000', "white" => "#ffffff"]
                        ],
                        "sectionBg" => "#ffffff",
                        "sectionBgAlternate" => "#ffffff",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'pixayogi' => [
                    'logo' => asset('media/logo/pixayogi-full.png'),
                    "name" => 'Pixayogi',
                    'icon' => 'assets/global/images/py.png',
                    'main_path' => 'components.pixayogi.main',
                    'product_id' => 66,
                    'btnColor' => ['normal' => '#44c9f5', 'disable' => '#168cd1', 'hover' => '#528fdd'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#6DD1F6',
                            'hover' => '#01b7fa',
                            'focus' => '#01b7fa',
                            'disable' => '#0078a4',
                            'text' => ["black" => '#000000', "white" => "#ffffff"]
                        ],
                        "sectionBg" => "#ffffff",
                        "sectionBgAlternate" => "#ffffff",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'rokdi' => [
                    'logo' => asset('media/logo/rokdi-full.png'),
                    "name" => 'Rokdi',
                    'icon' => 'assets/global/images/ro.png',
                    'main_path' => 'components.rokdi.main',
                    'product_id' => 15,
                    'btnColor' => ['normal' => '#910b2b', 'disable' => '#910b2b', 'hover' => '#910b2b'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#910b2b',
                            'hover' => '#ff0017',
                            'focus' => '#ff0017',
                            'disable' => '#7e4d51',
                            'text' => ["black" => '#000000', "white" => "#ffffff"]
                        ],
                        "sectionBg" => "#fef9e8",
                        "sectionBgAlternate" => "#ffffff",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'androsms' => [
                    'logo' => asset('media/logo/andro-full.png'),
                    'icon' => 'assets/global/images/as.png',
                    "name" => 'AndroSMS',
                    'main_path' => 'components.androsms.main',
                    'product_id' => 29,
                    'btnColor' => ['normal' => '#e13362', 'disable' => '#e13362', 'hover' => '#ef1550'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#A91EEF',
                            'hover' => '#741EB8',
                            'focus' => '#741EB8',
                            'disable' => '#683b8b',
                            'text' => 'white'
                        ],
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'clatos' => [
                    'logo' => asset('media/logo/clatos-full.png'),
                    "name" => "Clatos",
                    'icon' => 'assets/global/images/cl.png',
                    'main_path' => 'components.clatos.main',
                    'product_id' => 75,
                    'btnColor' => ['normal' => '#247dff', 'disable' => '#0064f6', 'hover' => '#0064f6'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#247DFF',
                            'hover' => '#0058da',
                            'focus' => '#0058da',
                            'disable' => '#00833f',
                            'text' => ["black" => '#000000', "white" => "#ffffff"]
                        ],
                        "sectionBg" => "#ecf6fe",
                        "sectionBgAlternate" => "#ffffff",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'rapbooster' => [
                    'logo' => asset('assets/rapbooster/images/logo/RapboosterLogo-d.png'),
                    'icon' => 'assets/global/images/rb.png',
                    "name" => "Rapbooster",
                    'main_path' => 'components.rapbooster.main',
                    'product_id' => 50,
                    'btnColor' => ['normal' => '#2d2eb0', 'disable' => '#2d2eb0', 'hover' => '#0e10e4'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#08B5EF',
                            'hover' => '#1888CF',
                            'focus' => '#1888CF',
                            'disable' => '#42798b',
                            'text' => ["black" => '#000000', "white" => "#ffffff"]
                        ],
                        "sectionBg" => "#e0f2fe",
                        "sectionBgAlternate" => "#ffffff",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'dunesfactory' => [
                    'logo' => asset('media/logo/df-full.png'),
                    'icon' => 'assets/global/images/df.png',
                    'main_path' => 'components.dunesfactory.main',
                    'product_id' => 1,
                    'btnColor' => ['normal' => '#2d2eb0', 'disable' => '#2d2eb0', 'hover' => '#0e10e4'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#08B5EF',
                            'hover' => '#1888CF',
                            'focus' => '#1888CF',
                            'disable' => '#42798b',
                            'text' => 'white'
                        ],
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'andro' => [
                    'logo' => asset('media/logo/andro-full.png'),
                    'icon' => 'assets/global/images/as.png',
                    'name' => "AndroSMS",
                    'main_path' => 'components.andro.main',
                    'product_id' => 1,
                    'btnColor' => ['normal' => '#e13362', 'disable' => '#2d2eb0', 'hover' => '#e13362'],
                    'theme' => [
                        'header' => ['bg' => 'white', 'color' => 'white'],
                        'footer' => ['bg' => 'white', 'color' => 'white'],
                        'btn' => [
                            'main' => '#e13362',
                            'hover' => '#e13362',
                            'focus' => '#e13362',
                            'disable' => '#e13362',
                            'text' => ["black" => '#000000', "white" => "#ffffff"]
                        ],
                        "sectionBg" => "#fef9e8",
                        "sectionBgAlternate" => "#F6F2FA",
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
                'texaplus' => [
                    'logo' => asset('media/logo/texaplus-252w.png'),
                    'icon' => 'assets/global/images/texaplus-FavIcon.png',
                    'main_path' => 'components.texaplus.main',
                    'product_id' => 29,
                    'btnColor' => ['normal' => '#e13362', 'disable' => '#e13362', 'hover' => '#ef1550'],
                    'theme' => [
                        'header' => ['bg' => 'green', 'color' => 'white'],
                        'footer' => ['bg' => 'green', 'color' => 'white'],
                        'btn' => [
                            'main' => '#A91EEF',
                            'hover' => '#741EB8',
                            'focus' => '#741EB8',
                            'disable' => '#683b8b',
                            'text' => 'white'
                        ],
                        'link' => ['main' => 'green', 'hover' => 'light-green', 'focus' => 'light-green'],
                        'font-color' => 'secondary'
                    ]
                ],
            ];

            // Build the result array
            $result = [];
            foreach ($domains as $key => $urls) {
                foreach ($urls as $url) {
                    $result[$url] = $domainData[$key];
                }
            }

            return $result;
        });

        if ($domain) {
            return $result[$domain] ?? abort(404);
        }

        return $result;
    }
}
if (!function_exists('sendOtp')) {
    function sendOtp($mobile)
    {
        $hostData = hostData();
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://control.msg91.com/api/v5/otp?template_id=$hostData->msg91TemplateId&mobile=$mobile",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode([
                'Param1' => 'value1',
                'Param2' => 'value2',
                'Param3' => 'value3'
            ]),
            CURLOPT_HTTPHEADER => [
                "accept: application/json",
                "authkey:$hostData->msg91AuthKey",
                "content-type: application/json"
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        $res = (json_decode($response, true));
        curl_close($curl);


        if ($err || !$res["type"] == "success") {
            // CreateLoginLog(null,  $mobile, 'Otp send Fail', false);
            return ['status' => false, 'message' => 'unable to send Otp.'];
        }
        return ['status' => true, 'message' => ' Otp send successfully.'];
    }
}

if (!function_exists('sendEmail')) {
    function  sendEmail($mobile)
    {
        $hostData = hostData();
        $host = request()->getHttpHost();
        $mail = new PHPMailer(true);
        // Email server settings
        $mail->SMTPDebug = 0;
        $mail->isSMTP();
        $mail->Host = $hostData->smtpHost;             //  smtp host
        $mail->SMTPAuth = true;
        $mail->Username = $hostData->smtpUsername;   //  sender username
        $mail->Password = $hostData->smtpPassword;       // sender password
        $mail->SMTPSecure = $hostData->smtpType;                  // encryption - ssl/tls
        $mail->Port = $hostData->smtpPort;                          // port - 587/465
        // Email server settings Ends
        $mail->setFrom($hostData->smtpUsername,  $hostData->name);
        $lead = User::where('mobile', $mobile)->first();

        $mail->addAddress($lead->email);
        // password generations start
        $otp =  random_int(100000, 999999);
        // storing otp in database
        if ($lead) {
            $lead->password = $otp;
            $lead->update();
        }
        // storing otp in database end
        // otp generation end
        $mail->isHTML(true);                       // Set email content format to HTML
        $mail->Subject = "Welcome to $host .";

        $mail->Body    = "<h1> Hello $lead->name </h1>  <br> <hr> <br>  Your  verification code for $host is <b><u> $otp </u></b>";

        if ($mail->send()) {
            return ['status' => true, 'message' => "email_sent"];
        } else {
            return ['status' => false, 'message' => "mail not send"];
        }
    }
}
if (!function_exists('cleanEmail')) {
    function cleanEmail($email)
    {
        // Use a regular expression to match and replace content between "+" and "@"
        return preg_replace('/\+(.*?)@/', '@', $email);
    }
}

if (!function_exists('getCartTotals')) {
    function getCartTotals($items)
    {
        $mrp = 0;
        $totalItems = 0;
        $totalGst = 0;
        $totalPayable = 0;
        $additionalDiscount = 0;
        $sellingPrice = 0;
        foreach ($items as $item) {
            $mrpVal = ($item->mrp < $item->sellingPrice) ? $item->sellingPrice : $item->mrp;
            $additionalDiscount = $item->discount * $item->product_quantity;
            $mrp += $mrpVal * $item->product_quantity;
            $sellingPrice += $item->sellingPrice * $item->product_quantity;
            $totalGst += $item->sellingPrice * $item->product_quantity * ($item->taxPercent / 100);
            foreach ($item->addons as $item) {
                $mrpVal = ($item->mrp < $item->sellingPrice) ? $item->sellingPrice : $item->mrp;
                $additionalDiscount = $item->discount * $item->product_quantity;
                $mrp += $mrpVal * $item->product_quantity;
                $sellingPrice += $item->sellingPrice * $item->product_quantity;
                $totalGst += $item->sellingPrice * $item->product_quantity * ($item->taxPercent / 100);
            }
        }
        $data['mrp'] = (int)round($mrp);
        $data['subTotal'] = (int)round($sellingPrice - $totalGst);
        $data['totalItems'] = (int)round($totalItems);
        $data['totalGst'] = (int)round($totalGst);
        $data['totalPayable'] = (int)round($sellingPrice - $additionalDiscount);
        $data['baseDiscount'] = round($mrp) - round($sellingPrice);
        $data['sellingPrice'] = (int)round($sellingPrice);
        $data['additionalDiscount'] = $additionalDiscount;
        return $data;
    }
}
if (!function_exists('getInvoiceTotal')) {
    function getInvoiceTotal($invoice)
    {
        $mrp = 0;
        $Discount = 0;
        $subTotal = 0;
        $totalItems = 0;
        $totalGst = 0;
        $totalPayable = 0;
        $totalSellingPrice = 0;
        $additionalDiscount = 0;
        foreach ($invoice->items as $value) {
            $mrpValue = ($value->sellingPrice > $value->mrp) ? $value->sellingPrice : $value->mrp;
            $mrp += $mrpValue * $value->quantity;
            $Discount += $value->discount;
            $subTotal += $value->subTotal;
            $totalItems += 1;
            $totalGst += $value->igst;
            $totalPayable += $value->total;
            $totalSellingPrice += $value->sellingPrice;
            $additionalDiscount += $value->discount * $value->quantity;
        }

        $data['totalSellingPrice'] = $totalSellingPrice;
        if ($totalPayable > $mrp) {
            $mrp = $totalPayable;
        }

        $data['mrp'] = $mrp;
        $data['subTotal'] = $subTotal;
        $data['totalItems'] = $totalItems;
        $data['totalGst'] = $totalPayable - $subTotal;
        $data['totalPayable'] = $totalPayable;
        if (($mrp - $totalPayable) <= 0) {
            $baDis = 0;
        } else {
            $baDis = $mrp - $totalPayable;
        }
        $data['baseDiscount'] = $baDis;
        $data['addonDisc'] = 0;
        $data['couponDisc'] = 0;
        $data['additionalDiscount'] = $additionalDiscount;
        return $data;
    }
}
if (!function_exists('generateInvoice')) {
    function generateInvoice($lead = null)
    {
        try {
            $userLead = strval($lead ?? auth()->id());

            $select = DB::select('select createInvoiceFromCart(?) as invoice', [$userLead]);
            $invoice = json_decode(json_encode(json_decode(json_encode($select))[0], true), true)['invoice']; //final in last version

            return ['status' => true, 'message' => 'Invoice Created', 'id' => $invoice];
        } catch (Exception $e) {
            return ['status' => false, 'message' => 'Unable to create Invoice.', 'devErr' => $e->getMessage()];
        }
    }
}

if (!function_exists('session_log')) {
    function session_log(string $sessionId, string $leadId = null, string $pageUrl, string $params, string $remark, string $error = null)
    {
        try {
            DB::table('session_log')->insert([
                'session_id' => $sessionId,
                'lead_id' => $leadId,
                'pageUrl' => $pageUrl,
                'params' => $params,
                'remark' => $remark,
                'error' => $error,
            ]);
            return ['status' => true];
        } catch (Exception $e) {
            return ['status' => false];
        }
    }
}
if (!function_exists('sendLoginEmailOTP')) {
    function  sendLoginEmailOTP($leadId, $email)
    {
        try {
            $emailProvider = EmailOtpProvider::where('emailProvider', "smtp")->where("isActive", "1")->with('creds')->first();
            if ($emailProvider) {
                $mail = new PHPMailer(true);
                // Email server settings
                $mail->SMTPDebug = 0;
                $mail->isSMTP();
                $mail->Host = $emailProvider->creds["host"];             //  smtp host
                $mail->SMTPAuth = true;
                $mail->Username = $emailProvider->creds['userName'];   //  sender username
                $mail->Password = $emailProvider->creds['password'];       // sender password
                $mail->SMTPSecure = $emailProvider->creds['type'];                  // encryption - ssl/tls
                $mail->Port = $emailProvider->creds['port'];                          // port - 587/465
                // Email server settings Ends
                $mail->setFrom($emailProvider->creds['senderEmail']);
                // if ($email == null) {
                // } else {
                // $leadEmail = Lead_email::firstOrCreate(['email' => $email], ["lead_id" => $leadId]);
                // }

                $leadEmail = Lead::where("id", $leadId)->first();
                $mail->addAddress($email);
                $otp =  random_int(100000, 999999);
                if ($leadEmail) {
                    $leadEmail->emailOtp = $otp;
                    $leadEmail->emailOtpExpiry = now();
                    $leadEmail->update();
                }
                $mail->isHTML(true);                       // Set email content format to HTML
                $mail->Subject = $emailProvider->creds['emailSubject'];
                $mail->Body    = $emailProvider->creds['emailMessage'] . " " . " <b>" . $otp . "</b>";

                if ($mail->send()) {
                    DB::table('smtp_log')->insert(['smtp_creds_id' => $emailProvider->creds['id'], "params" => json_encode(["email" => $email, "otp" => $otp]), "remark" => "OTP Send Successfully"]);
                    return ['status' => true, 'message' => "email_sent", "otpLength" => $emailProvider->creds["otpLength"]];
                } else {
                    DB::table('smtp_log')->insert(['smtp_creds_id' => $emailProvider->creds['id'], "params" => json_encode(["email" => $email]), "remark" => "OTP Send Failed", "error" => "mail not send"]);
                    return ['status' => false, 'message' => "Internal Server Issue"];
                }
            } else {
                DB::table('smtp_log')->insert(['smtp_creds_id' => null, "params" => json_encode(["lead_id" => $leadId, "email" => $email]),  "remark" => "smtp creds not found", "error" => "smtp creds not found"]);
                return ['status' => false, 'message' => 'No Service Provider Found'];
            }
        } catch (Exception $e) {
            if (!(DB::table('smtp_log')->insert(['smtp_creds_id' => $emailProvider->creds['id'] ?? null, "params" => json_encode(["email" => $email]), "remark" => "OTP Send Failed", "error" => $e->getMessage()]))) {
                Log::info(json_encode([$leadId ?? 'lead not available.', $email ?? 'email not available.', 'email not send.', $e->getMessage()]));
            }
            return ['status' => false, 'message' => "mail not send"];
        }
    }
}
if (!function_exists('sendMobileOtp')) {
    function sendMobileOtp($leadId, $mobile)
    {
        $creds_id = DB::table('sms_otp_provider')->where('provider', "msg91")->where('isActive', "1")->first();
        if ($creds_id) {
            $creds = DB::table('msg91_creds')->where('id', $creds_id->msg91_creds_id)->first();
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://control.msg91.com/api/v5/otp?template_id=$creds->templateId&mobile=$mobile",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode([
                    'Param1' => 'value1',
                    'Param2' => 'value2',
                    'Param3' => 'value3'
                ]),
                CURLOPT_HTTPHEADER => [
                    "accept: application/json",
                    "authkey:$creds->apiKey",
                    "content-type: application/json"
                ],
            ]);
            $response = curl_exec($curl);
            $err = curl_error($curl);
            $res = (json_decode($response, true));
            curl_close($curl);

            if ($err || !$res["type"] == "success") {
                DB::table('msg91_log')->insert(['msg91_creds_id' => $creds_id["msg91_creds_id"], "params" => json_encode(["lead_id" => $leadId, "mobile" => $mobile]), "response" => $err, "remark" => "OTP send failed", "error" => "OTP send failed"]);
                return ['status' => false, 'message' => 'unable to send Otp.'];
            }
            DB::table('msg91_log')->insert(['msg91_creds_id' => $creds_id->msg91_creds_id, "params" => json_encode(["lead_id" => $leadId, "mobile" => $mobile]), "response" => $response, "remark" => "OTP send Success"]);
            return ['status' => true, 'message' => 'Otp send successfully.', 'otpLength' => $creds->otpLength];
        } else {
            DB::table('msg91_log')->insert(['msg91_creds_id' => $creds_id["msg91_creds_id"] ?? null, "params" => json_encode(["lead_id" => $leadId, "mobile" => $mobile]), "response" => "msg91 creds not found", "remark" => "OTP send failed", "error" => "msg91 creds not found"]);
            return ['status' => false, 'message' => 'No Service Provider Found'];
        }
    }
}
if (!function_exists('mobileOtpVerify')) {
    function mobileOtpVerify($leadId, $otp, $mobile)
    {
        $creds_id = DB::table('sms_otp_provider')->where('provider', "msg91")->where("isActive", 1)->first();
        if ($creds_id) {
            $creds = DB::table('msg91_creds')->where('id', $creds_id->msg91_creds_id)->first();
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://control.msg91.com/api/v5/otp/verify?otp=$otp&mobile=$mobile",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => [
                    "accept: application/json",
                    "authkey: $creds->apiKey"
                ],
            ]);
            $response = curl_exec($curl);
            $response = json_decode($response, true);
            if ($response["type"] == "error") {
                DB::table('msg91_log')->insert(['msg91_creds_id' => $creds_id->msg91_creds_id, "params" => json_encode(["lead_id" => $leadId, "mobile" => $mobile]), "response" => json_encode($response), "remark" => "OTP verify failed", "error" => $response["message"]]);
                return ['status' => false, 'message' => $response["message"]];
            }
            DB::table('msg91_log')->insert(['msg91_creds_id' => $creds_id->msg91_creds_id, "params" => json_encode(["lead_id" => $leadId, "mobile" => $mobile]), "response" => json_encode($response), "remark" => "OTP verified successfully"]);
            return ["status" => true, "message" => "Verified Successfully"];
        } else {
            DB::table('msg91_log')->insert(['msg91_creds_id' => null, "params" => json_encode(["lead_id" => $leadId, "mobile" => $mobile]), "response" => "msg91 creds not found", "remark" => "OTP verify failed", "error" => "msg91 creds not found"]);
            return ['status' => false, 'message' => 'vendor_not_found'];
        }
    }
}
if (!function_exists('productPrice')) {
    function productPrice(array $id)
    {
        $products = Product::where('isActive', 1)->WhereIn("id", $id)->get();
        foreach ($products as $value) {
            $dataArray[$value->id] = ["mrp" => (int)$value->mrp, "sellingPrice" => (int)$value->sellingPrice];
        }
        return $dataArray;
    }
}

if (!function_exists('maskEmail')) {

    function maskEmail($email, $numChars = 4)
    {
        $parts = explode('@', $email);
        $username = substr($parts[0], 0, 3);

        // Handle cases where username length is less than 3
        if (strlen($username) < 3) {
            $username = $parts[0]; // Show entire username if less than 3 characters
        } else {
            // Mask remaining characters with asterisks
            $username .= str_repeat('*', strlen($parts[0]) - 3);
        }

        return $username . '@' . $parts[1];
    }
}

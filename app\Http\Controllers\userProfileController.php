<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Lead;
use App\Models\Leadfor;
use App\Models\License;
use App\Models\Order;

use App\Models\SupportTicket;

use App\Models\Transaction;
use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class userProfileController extends Controller
{
    public function edit(Request $request)
    {
        $lead_for = DB::table("lead_for")->get();
        return view("userpages.profileEdit", compact("data", "lead_for"));
    }

    public function userProfile()
    {
        $data['profiles'] = User::where('parent_id', Auth::id())->orwhere('id', auth()->id())->orderBy('parent_id', 'asc')->paginate(4);
        return view('userpages.profile.index', $data);
    }
    public function userOrders(Request $request)
    {

        $data['sort'] = $sort = 'DESC';
        if (isset($_GET['sort'])) {

            $data['sort'] = $sort = $_GET['sort'] == 'DESC' ? 'ASC' : 'DESC';
        }
        $search = $request->search ?? null;
        if ($search) {
            $search = trim($search);

            $data = Order::where('date', 'like', '%' . $search . '%')
                ->orWhere('remarks', 'like', '%' . $search . '%')
                ->orWhereHas('invoice', function (Builder $query) use ($search) {
                    $query->where('invoiceNumber', 'like', '%' . $search . '%');
                });
        } else {
            $data = Order::query();
        }
        $data = $data->where('lead_id', Auth::user()->id)->with('orderItems', 'invoice')->paginate(10)->withQueryString();
        return view('userpages.orders', compact('data', 'sort', 'search'));
    }
    public function userInvoices(Request $request)
    {
        $data['sort'] = $sort = 'DESC';
        if (isset($_GET['sort'])) {

            $data['sort'] = $sort = $_GET['sort'] == 'DESC' ? 'ASC' : 'DESC';
        }

        $search = $request->search ?? null;
        if ($search) {
            $search = trim($search);

            $data = Invoice::where('date', 'like', '%' . $search . '%')
                ->orWhere('invoiceNumber', 'like', '%' . $search . '%')
                ->orWhere('name', 'like', '%' . $search . '%')
                ->orWhere('company', 'like', '%' . $search . '%')
                ->orWhere('mobile', 'like', '%' . $search . '%')
                ->orWhere('expiryDate', 'like', '%' . $search . '%')
                ->orWhere('gst', 'like', '%' . $search . '%')
                ->orWhere('city', 'like', '%' . $search . '%')
                ->orWhere('state', 'like', '%' . $search . '%')
                ->orWhere('address', 'like', '%' . $search . '%')
                ->orWhere('email', 'like', '%' . $search . '%')
                ->orWhere('pincode', 'like', '%' . $search . '%');
        } else {
            $data = Invoice::query();
        }
        $data = $data->orderBy('date', $sort)
            ->where('lead_id', Auth::id())
            ->where('isPaid', 1)
            ->with('items')
            ->paginate(9)->withQueryString();
        return view('userpages.invoices', compact("data", 'sort', 'search'));
    }
    function userAddress()
    {
        $data = User::whereId(Auth::id())->with('addresses')->first();
        $addresses = $data->addresses()->paginate('6', ['*'], 'addressPage');
        return view('userpages.address', compact('data', 'addresses'));
    }
    //     userAddress
    public function userLicences(Request $request)
    {

        $sortBy = $request->sortBy ?? 'datetime';
        $data['sort'] = $sort = 'DESC';
        if (isset($_GET['sort'])) {

            $data['sort'] = $sort = $_GET['sort'] == 'DESC' ? 'ASC' : 'DESC';
        }

        $search = $request->search ?? null;
        if ($search) {
            $search = trim($search);
            $data = License::whereAny([
                'datetime',
                'nameOnSoftware',
                'key',
                'version',
                'activationDateTime',
                'validity',
                'expiryDateTime',
                'remarks',
            ], 'like', '%' . $search . '%');
         

        } else {
            $data = License::query();
        }
        $data = $data
            ->orderBy($sortBy, $sort)
            ->where('lead_id', auth()->id())
            ->with('product')
            ->paginate(6)->withQueryString();
        // dd($data->toArray());
        return view('userpages.licences', compact("data", 'search', 'sort'));
    }
    public function usertransactions(Request $request)
    {
        $sortBy = $request->sortBy ?? 'transactionDateTime';
        $data['sort'] = $sort = 'DESC';
        if (isset($_GET['sort'])) {

            $data['sort'] = $sort = $_GET['sort'] == 'DESC' ? 'ASC' : 'DESC';
        }
        $search = $request->search ?? null;
        if ($search) {
            $search = trim($search);
            $data = Transaction::where('transactionDateTime', 'like', '%' . $search . '%')
                ->orWhere('amount', 'like', '%' . $search . '%')
                ->orWhere('remarks', 'like', '%' . $search . '%')
                ->orWhere('transactionId', 'like', '%' . $search . '%');
        } else {
            $data = Transaction::query();
        }

        $data = $data
            ->where('lead_id', Auth::user()->id)
            ->where('isApproved', 1)
            ->orderBy($sortBy, $sort)
            ->with("paymentMethod")
            ->paginate(12)->withQueryString();
        $leadBalance = Lead::where("id", Auth::id())->first(["balance"]);
        return view('userpages.transactions', compact("data", 'search', 'sort', 'leadBalance'));
    }
    /*
    this function is used for userSupportTickets ,with search, sort  funcnality

    */
    public function userSupportTickets(Request $request)
    {
        $page = $request->page ?? null;
        $sortBy = $request->sortBy ?? 'datetime';
        $data['sort'] = $sort = 'DESC';
        if (isset($_GET['sort'])) {
            $data['sort'] = $sort = $_GET['sort'] == 'DESC' ? 'ASC' : 'DESC';
        }
        $search = $request->search ?? null;

        $data = SupportTicket::with('product')
            ->where('lead_id', Auth::user()->id)
            ->orderBy($sortBy, $sort)
            ->paginate(9)->withQueryString();
        return view('userpages.supportTickets', compact("data", 'search', 'sort', 'page'));
    }
}

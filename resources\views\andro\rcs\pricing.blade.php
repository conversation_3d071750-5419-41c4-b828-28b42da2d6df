@extends('components.andro.main')
@section('webpage')
    @include('components.andro.rcs.sub-nav')
    {{-- FIRST SECTION  --}}
    <section class="mt-5 pb-lg-5 pt-lg-4 mt-lg-0">
        <div class="container-fluid bg-light-shade rounded-5 p-t-feature w-90"
            style="background-image: url({{ asset('assets/androsms/images/h1-bg.png') }}); background-repeat: no-repeat;background-size: contain">
            <div class="container-lg d-flex flex-column align-items-center">
                <h2 class="text-pink fw-bold text-center androsms-h2"><span class="text-purple">Select a plan and</span>
                    expand your
                    business</h2>
                <p class="fs-5 text-center">Discover flexible plans for businesses of all sizes. Select the perfect solution
                    aligned with your goals, unlocking powerful features for business growth. Embrace success with
                    transparent pricing and exceptional support.</p>
                <div class="max-width-btn">

                    <div class="d-flex flex-row gap-3">
                        {{-- {{ route('andro.contact') }} --}}
                        <a aria-label="link" class="text-decoration-none" href="{{ route('andro.contact') }}">
                            <button
                                class="rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-lg-4 ps-lg-4 pe-2 ps-2 d-flex gap-3 pink-btn-shadow pink-btn text-white">
                                <div class="d-flex fs-5">Talk to a human</div>
                                <div class="">
                                    <img style="height: 35px;" src="{{ asset('assets/global/images/button.webp') }}"
                                        alt="">
                                </div>
                            </button>
                        </a>

                        @php
                            $getstartedTarget = '/contact';
                        @endphp
                        <a aria-label="link"
                            class="text-decoration-none pink-btn text-white rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                            href="{{ $getstartedTarget }}">
                            Get started
                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                        </a>
                    </div>

                </div>
            </div>
        </div>
    </section>

    {{-- SECOND SECTION  --}}
    <div class="container-fluid pt-5 pb-5 pt-lg-4 pb-lg-0 overflow-hidden">
        <div class="container-lg pb-lg-5">
            <div class="row d-flex align-items-center flex-column-reverse flex-lg-row flex-md-row">
                <div class="col-lg-6 col-md-6 pe-lg-5 pb-md-4 pt-lg-0 pt-4">
                    <h3 class="fw-bold">It's easy to Get started</h3>
                    <p>Experience our industry-leading service at the best prices guaranteed. Unmatched value for your
                        business needs. Join thousands of satisfied customers who have elevated their success with AndroSMS.
                        Take your business to new heights with our reliable and cost-effective solutions. Trust in our
                        expertise and customer-centric approach to streamline your communication strategies and achieve
                        unparalleled results.</p>
                    <div class="max-width-btn">
                        @php

                            $getstartedTarget = '/contact';

                        @endphp
                        <a aria-label="link" class="text-decoration-none" href="{{ $getstartedTarget }}">
                            <button
                                class="pink-btn text-white rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2 fs-5">
                                Get started
                                <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                            </button></a>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 text-center" data-aos="fade-left">
                    <img class="img-fluid" src="{{ asset('assets/androsms/images/plan1 1.png') }}" alt="">
                </div>
            </div>
        </div>
    </div>

    {{-- Pricing-Section Start --}}
    <div class="container-fluid pb-5">
        <div class="container-lg">
            <div class="price-grid">
                <div class="price-items-1">
                    <h4 class="text-center fw-bolder">Premium</h4>
                    <p class="price-para">Connect with up to <strong>5 devices</strong> for expanded reach.</p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn  price-items-1-btn px-4 py-1 text-dark">
                                    Add to cart</a>
                            @else
                                <button class="btn  price-items-1-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn  price-items-1-btn px-4 py-1 text-dark">Login to see
                                Price</a>
                        @endif
                    </div>
                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 5 devices for efficient communication.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Enjoy advanced SMS scheduling features.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Seamlessly integrate with APIs for enhanced functionality.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Set up automated replies for instant engagement.</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-1">
                    <h4 class="text-center fw-bolder">Standard</h4>
                    <p class="price-para">Expand your network with support for <strong>10 devices.</strong></p>
                    {{-- <div class="text-center"> --}}
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn  price-items-1-btn px-4 py-1 text-dark">Add to cart</a>
                            @else
                                <button class="btn  price-items-1-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn  price-items-1-btn px-4 py-1 text-dark">Login to see
                                Price</a>
                        @endif
                    </div>

                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 10 devices for expanded reach.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Utilize SMS scheduling for efficient messaging.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Integrate with APIs for seamless system integration.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Enable auto replies to enhance customer interaction.</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-2 mt-4 ">
                    <button class="most-popular-blue pt-2 pb-3 pe-4 ps-4 mb-3">
                        Most Popular
                    </button>
                    <h4 class="text-center fw-bolder">Basic</h4>
                    <p class="price-para">Reach a wider audience with support for <strong>15 devices</strong></p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn price-items-2-btn px-4 py-1 text-dark">Add to cart</a>
                            @else
                                <button class="btn price-items-2-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn price-items-2-btn px-4 py-1 text-light">Login to
                                see
                                Price</a>
                        @endif
                    </div>

                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 15 devices for wider coverage.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Access essential SMS scheduling features.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Integrate with APIs for streamlined operations.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Set up automated replies for prompt customer response.</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-3">
                    <h4 class="text-center fw-bolder">Advanced</h4>
                    <p class="price-para">Extend your messaging capabilities with up to <strong>20 devices</strong></p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn  price-items-3-btn px-4 py-1 text-dark">Add to cart</a>
                            @else
                                <button class="btn  price-items-3-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn  price-items-3-btn px-4 py-1 text-dark">Login to
                                see Price</a>
                        @endif
                    </div>

                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 20 devices for extensive communication.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Utilize advanced SMS scheduling options.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Seamlessly integrate with APIs for enhanced functionality.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Set up auto replies to improve customer engagement.</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-3">
                    <h4 class="text-center fw-bolder">Pro</h4>
                    <p class="price-para">Tailored solutions to meet your specific requirements.</p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn  price-items-3-btn px-4 py-1 text-dark">Add to cart</a>
                            @else
                                <button class="btn  price-items-3-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn  price-items-3-btn px-4 py-1 text-dark">Login to
                                see Price</a>
                        @endif
                    </div>

                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Customized device connections to meet your requirements.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Tailored SMS scheduling for efficient communication.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Seamless API integration for system compatibility.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Personalized automated replies for efficient interactions.</li>
                        </div>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    {{-- Pricing-Section End --}}
    @php
        $faqs = [
            [
                'question' => 'What are the benefits of using bulk SMS?',
                'answer' =>
                    'Bulk SMS enables businesses to reach a wide audience instantly, improve customer engagement, and deliver time-sensitive information efficiently, ultimately boosting brand visibility and increasing response rates.',
            ],
            [
                'question' => 'Is opt-in necessary for bulk SMS?',
                'answer' =>
                    'Yes, compliance with regulations is vital, and recipients must provide explicit consent (opt-in) before receiving bulk SMS messages to ensure a legitimate and respectful communication channel.',
            ],
            [
                'question' => 'How are contacts managed in bulk SMS?',
                'answer' =>
                    'Contacts can be managed through the bulk SMS platform, where you can organize lists, add or remove subscribers, and segment your audience based on specific criteria for targeted messaging.',
            ],
            [
                'question' => 'Can bulk SMS be personalized?',
                'answer' =>
                    'Yes, most bulk SMS services allow personalization, enabling businesses to include individualized details like names, purchase history, or other custom information to make messages more relevant to recipients.',
            ],
            [
                'question' => 'Are there any message length restrictions in bulk SMS?',
                'answer' =>
                    'Yes, standard SMS messages are limited to 160 characters. However, some platforms allow you to send longer messages by concatenating multiple SMS into one long message.',
            ],
            [
                'question' => 'Can multimedia be included in bulk SMS?',
                'answer' =>
                    'Yes, some platforms support Multimedia Messaging Service (MMS), which allows you to send multimedia content like images, videos, audio, or vCards, enhancing the impact of your messages.',
            ],
            [
                'question' => 'How can businesses track the effectiveness of their bulk SMS campaigns?',
                'answer' =>
                    'Bulk SMS platforms provide delivery reports and analytics that show the status of each sent message, delivery rates, and click-through rates, allowing businesses to evaluate campaign performance.',
            ],
        ];
    @endphp
    <section class="container-fluid py-4 bg-light-shade w-90 rounded-5">
        <div class="container-lg ">
            <h3 class="h3-custom text-center text-dark fs-1 fw-bold mb-4">Frequently Asked Questions</h3>
            <x-faq-accordion :faqs="$faqs" collapsed-color="#f9f9f9" active-color="#e13362" />
        </div>
    </section>
    @include('components.andro.earn')
@endsection

<?php $__env->startSection('only-Page-css'); ?>

    <style id="" media="all">
        /* cyrillic-ext */
        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(/fonts.gstatic.com/s/montserrat/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCvC73w0aXpsog.woff2) format('woff2');
            unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
        }



        #notfound {
            position: relative;
            height: 70vh
        }

        #notfound .notfound {
            position: absolute;
            left: 50%;
            top: 50%;
            -webkit-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%)
        }

        .notfound {
            max-width: 520px;
            width: 100%;
            line-height: 1.4;
            text-align: center
        }

        .notfound .notfound-404 {
            position: relative;
            height: 240px
        }

        .notfound .notfound-404 h1 {
            font-family: montserrat, sans-serif;
            position: absolute;
            left: 50%;
            top: 50%;
            -webkit-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            font-size: 252px;
            font-weight: 900;
            margin: 0;
            color: #262626;
            text-transform: uppercase;
            letter-spacing: -40px;
            margin-left: -20px
        }

        .notfound .notfound-404 h1>span {
            text-shadow: -8px 0 0 #fff
        }

        .notfound .notfound-404 h3 {
            font-family: cabin, sans-serif;
            position: relative;
            font-size: 16px;
            font-weight: 700;
            text-transform: uppercase;
            color: #262626;
            margin: 0;
            letter-spacing: 3px;
            padding-left: 6px
        }

        .notfound h2 {
            font-family: cabin, sans-serif;
            font-size: 20px;
            font-weight: 400;
            text-transform: uppercase;
            color: #000;
            margin-top: 0;
            margin-bottom: 25px
        }

        @media only screen and (max-width: 767px) {
            .notfound .notfound-404 {
                height: 200px
            }

            .notfound .notfound-404 h1 {
                font-size: 200px
            }
        }

        @media only screen and (max-width: 480px) {
            .notfound .notfound-404 {
                height: 162px
            }

            .notfound .notfound-404 h1 {
                font-size: 162px;
                height: 150px;
                line-height: 162px
            }

            .notfound h2 {
                font-size: 16px
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('title', '404 Page not Found'); ?>
<?php $__env->startSection('userpagesection'); ?>
    <div id="notfound">
        <div class="notfound">
            <div class="notfound-404">
                <h6>Oops! Page not found</h6>
                <h1><span>4</span><span>0</span><span>4</span></h1>
            </div>
            <h5>we are sorry, but the page you requested was not found</h5>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('userpages.cart.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\websites_laravel\resources\views/errors/404.blade.php ENDPATH**/ ?>
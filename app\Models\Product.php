<?php

namespace App\Models;


use App\Models\productAddon;
use App\Models\ProductFeature;
use App\Models\ProductGallery;
use App\Models\ProductVariant;
use App\Models\ProductCategory;
use App\Models\ProductVariantDistinct;
use App\Models\ProductVariantQtyRange;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory;
    protected $table = 'product';
    public $timestamps = false;

    public function images()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'id')->orderBy('sortOrder', 'asc');
    }
    public function primaryImage()
    {
        return $this->hasOne(ProductGallery::class, 'product_id', 'id')->orderByDesc('isPrimary');
    }
    public function variants()
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'id')
           ->where(['isActive'=>1,'isOnWebsite'=>1]);
    }
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id', 'id');
    }

    public function features()
    {
        return $this->hasMany(ProductFeature::class, 'product_id', 'id')->where('isActive', 1);
    }

    public function addons()
    {
        return $this->hasMany(productAddon::class, 'product_id', 'id')->with('product');
    }

    public function addonsProducts()
    {
        return $this->hasManyThrough(Product::class, productAddon::class, 'product_id'  ,'id')->with('distinctVariants');
    }

    // public function qtyRanges()
    // {
    //     return $this->hasMany(ProductVariantQtyRange::class, 'product_id', 'id')->where('isActive', 1);
    // }

    public function distinctVariants()
    {
        return $this->hasMany(ProductVariantDistinct::class, 'product_id', 'id')->where(['isActive' => 1, 'isOnWebsite' => 1])->with('discountRanges');
    }
    // public function validities()
    // {
    //     return $this->hasMany(ProductValidity::class, 'product_id', 'id')->where('isActive', 1);
    // }

}

<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ProductVariantDistinct;
use App\Models\ProductVariantQtyRange;

class ProductController extends Controller
{
    public function product($category, $categoryId, $productVariant)
    {
        try {
            $rangeVariant = ProductVariant::find($productVariant);

            $data = [
                'rangeVariant' => $rangeVariant,
                'productVariants' => ProductVariantDistinct::where('product_id', $rangeVariant->product_id)
                    ->with('discountRanges', 'product:id,name')
                    ->get(),
                'productVariant' =>  $rangeVariant,
                'bulkDiscounts' => ProductVariantQtyRange::where([
                    'name' => $rangeVariant->name,
                    'product_id' => $rangeVariant->product_id,
                    'validity' => $rangeVariant->validity
                ])->orderBy('minQty')->get(),
            ];

            $data['product'] = Product::with([
                'category',
                'features',
                'addonsProducts',
                'distinctVariants',
                'variants'
            ])->findOrFail($rangeVariant->product_id);

            $data['mainProduct'] = [
                'first' => $data['bulkDiscounts']->first(),
                'last' => $data['bulkDiscounts']->last()
            ];
            $data['addons'] = $data['product']->addonsProducts;
            $data['addonDiscounts'] = ProductVariantQtyRange::whereIn(
                'product_id',
                $data['addons']->pluck('id')
            )->orderBy('minQty')->get();
            $data['hidePrice'] = auth()->check() && auth()->user()->hidePrice == 0;

            return view('global.ProductView', $data);
        } catch (Exception $e) {
   
            return redirect('/')->with('error', 'Invalid Request.');
        }
    }
}

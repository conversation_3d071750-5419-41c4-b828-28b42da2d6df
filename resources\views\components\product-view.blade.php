@if ($product->distinctVariants->count() != 0)
<div class="products-box-item-wrapper  mb-2 " id="products-box-{{ $product->id }}">
    <div class="d-flex products-box-item flex-column p-3 rounded-4 h-100">
        <div class="w-100 text-center mb-2 rounded-0 d-flex align-items-center justify-content-center"
            style="min-height:280px">
            @if ($product->images->count() > 1)
            <div class="swiper img-swiper">
                <div class="swiper-wrapper">
                    @foreach ($product->images as $image)
                    <div class="swiper-slide">
                        <img src="{{ s3_fileShow($image->image, 'product_gallery', $product->name) }}"
                            alt="" class="img-fluid rounded-2">
                    </div>
                    @endforeach
                </div>
                <div class="swiper-pagination"></div>
            </div>
            @else
            <a href="{{ route('product.view', ['categoryId'=>$product->category->id,'category' => strtolower(str_replace(' ','-',$product->category->name)), 'productVariant' => $product->distinctVariants[0]->id]) }}"
                class="text-decoration-none">
                <img src="{{ asset('media/logo/RapboosterLogo-d.png') }}" alt=""
                    class="img-fluid rounded-0">
            </a>
            @endif
        </div>

        <div class=" p-1 d-flex justify-content-between flex-column h-100">
            <div class="text-start">
                <a  href="{{ route('product.view', ['categoryId'=>$product->category->id,'category' => strtolower(str_replace(' ','-',$product->category->name)), 'productVariant' => $product->distinctVariants[0]->id]) }}"
                    class="text-decoration-none">
                    <div class="mb-2 product-detail-name text-dark">
                        {{ $product->name }}
                    </div>
                </a>
                @if (auth()->check() && auth()->user()->hidePrice == 0)
                <div class="mb-2 fw-semibold  gap-3 ">
                    <div class="product-detail-price-selling lh-1 mb-2">
                        <span class="fw-semibold  " style="font-size: 18px;">
                            ₹
                        </span>
                        <span id="" class="fw-semibold productSelling{{ $product->id }}">
                            {{ $product->distinctVariants[0]->sellingPrice }}
                        </span>

                        <span class="fw-semibold  " style="font-size: 12px;">
                            incl. TAX
                        </span>

                    </div>
                    <div class="product-detail-price-mrp">M.R.P:
                        <span class="product-mrp">
                            ₹{{ $product->distinctVariants[0]->mrp }}
                        </span>
                    </div>
                </div>
                @endif

            </div>
            @if (auth()->check() && auth()->user()->hidePrice == 0)
            <form action="{{ route('addToCartForm') }}" method="POST">
                @csrf
                <input type="hidden" name="quantity">
                <div class="d-flex gap-2 mt-2 flex-column">
                    <div class="mb-2">
                        <select name="productVariant" id=""
                            class="form-select  w-100 product-variant-select">
                            @foreach ($product->distinctVariants as $variant)
                            <option value="{{ $variant->id }}" data-selling="{{ $variant->sellingPrice }}"
                                data-product="products-box-{{ $product->id }}"
                                data-productminqtydiv="productminqty{{ $product->id }}"
                                data-productminqtyval="{{ $variant->minQty }}"
                                data-productselling="productSelling{{ $product->id }}">
                                {{ $variant->name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-2">
                        <button class=" btn  btn-warning rounded-pill text-white w-100">
                            Add to cart
                        </button>
                    </div>

                </div>
            </form>
            @endif
        </div>

    </div>
</div>
@endif

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Aws\S3\Exception\S3Exception;
use Illuminate\Support\Facades\DB;
use App\Models\LeadQuotationRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\File;

class LeadQuotationRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return redirect()->back();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $leadfor = DB::table('lead_for')->get();

        $budget = [
            ['value' => 50000, 'name' => '0 - 50k '],
            ['value' => 500000, 'name' => '50k - 5L'],
            ['value' => 100000, 'name' => '5L - 10L'],
            ['value' => 1, 'name' => 'Not Sure'],
        ];
        $modeOfCCommunications = [
            [
                'name' => 'Call',
                'value' => 0,
            ],
            [
                'name' => 'E-Mail',
                'value' => 1,
            ],
            [
                'name' => 'Whatsapp',
                'value' => 2,
            ],
        ];
        $preferredTimes = [
            ['name' => 'Any Time', 'value' => 0],
            ['name' => 'Morning', 'value' => 1],
            ['name' => 'Afternoon', 'value' => 2],
            ['name' => 'Evening', 'value' => 3],
        ];
        $product = $_GET['product'] ?? '';
        return view('userpages.quotation.create', compact('leadfor', 'budget', 'product', 'modeOfCCommunications', 'preferredTimes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'requirement' => 'required',
                'budget' => 'required',
                'mode' => 'required',
                'preferredTime' => 'required',
                'details' => 'required',
                'attachment' => [
                    File::types(['zip', 'pdf', 'png', 'bmp', 'jpg', 'jpeg', 'gif'])
                        ->max(20 * 1024),
                ],
            ],
            [
                'requirement.required' => 'Select at Least one Service.',
                'budget.required' => 'Select Budget Criteria.',
                'details.required' => 'Summarize your Requirement so that we Can provide service.',
            ],
            [
                'requirement' => 'Requirement',
                'budget' => 'Budget',
                'mode' => 'Mode of contact',
                'preferredTime' => 'Preferred Time',
                'details' => 'Details of Requirement',
                'attachment' => 'Attachment',
            ],
        );
        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $data = new LeadQuotationRequest();
            $data->requirement = implode(',', $request->requirement);
            $data->budget = $request->budget;
            $data->product_id = $request->product_id ?? null;
            $data->preferredModeOfContact = $request->mode;
            $data->preferredTime = $request->preferredTime;
            $data->details = $request->details;
            $data->lead_id = Auth::user()->id;
            if ($file = $request->file('attachment')) {
                try {
                    $path = s3_fileUpload($file, 'lead_quotation_request');
                } catch (S3Exception $e) {
                    return redirect()
                        ->back()
                        ->with('error', $e->getMessage())
                        ->withInput();
                }

                if ($path) {
                    $data->attachment = $path;
                } else {
                    $data->save();
                    return redirect()->back()->with('info', "We have successfully registered your request; however, it appears that your file has not been uploaded.");
                }
            }
            $data->save();
        } catch (QueryException $e) {
            return redirect()
                ->back()
                ->with('error', $e->getMessage())
                ->withInput();
        }
        return redirect('/')

            ->with('success', 'Quotation Created successfully. Our executive will connect you soon.');
    }
    public function modalStore(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'budget' => 'required',
                'mode' => 'required',
                'preferredTime' => 'required',
                'details' => 'required',
                'attachment' => 'max:20480',
            ],
            [
                'requirement.required' => 'Select at Least one Service.',
                'budget.required' => 'Select Budget Criteria.',
                'details.required' => 'Summarize your Requirement so that we Can provide service.',
            ],
            [
                'requirement' =>   'Requirement',
                'budget' =>   'Budget',
                'mode' =>   'Mode of contact',
                'preferredTime' =>   'Preferred Time',
                'details' =>   'Details of Requirement',
                'attachment' =>   'Attachment',
            ]
        );
        if ($validator->fails()) {
            dd($validator);
            return back()
                ->withErrors($validator)
                ->withInput();
        }
        try {
            $data = new LeadQuotationRequest();
            $host = request()->getHttpHost();
            $product_id = allDomains()[$host]['product_id'];
            $data->requirement =  json_encode(explode(',', $product_id));
            $data->budget = $request->budget;
            $data->preferredModeOfContact = $request->mode;
            $data->preferredTime = $request->preferredTime;
            $data->details = $request->details;
            $data->lead_id = Auth::user()->id;
            if ($file = $request->file('attachment')) {
                try {
                    $path = s3_fileUpload($file, 'lead_quotation_request');
                } catch (S3Exception $e) {
                    return redirect()
                        ->back()
                        ->with('error', $e->getMessage())
                        ->withInput();
                }
                if ($path) {
                    $data->attachment = $path;
                } else {
                    $data->save();
                    return redirect()->back()->with('info', "We have successfully registered your request; however, it appears that your file has not been uploaded.");
                }
            }
            $data->save();
            dd($data);
        } catch (QueryException $e) {
            dd($e);
            return redirect()
                ->back()
                ->with('error', $e->getMessage())
                ->withInput();
        }
        return redirect()
            ->back()
            ->with('success', 'Quotation Created successfully.');
    }
}

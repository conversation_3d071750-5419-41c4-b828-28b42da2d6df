@php
    $host = request()->getHttpHost();
    $hostdataArray = allDomains()[$host];
    $main_path = $hostdataArray['main_path'];
    $icon = $hostdataArray['icon'];
@endphp
@section('siteIcon', asset($icon))
@extends($main_path)
@section('title', 'Profile')
@section('PAGE-CSS')
    <link rel="stylesheet" href="{{ asset('assets/userpages/customer.css') }}">
    @if ($host == 'localhost:8001' || $host == 'wabhai.com' || $host == 'wabhai.orkia.in')
        <style>
            /* Extra small devices (phones, 600px and down) */
            @media only screen and (max-width: 600px) {
                .c-margin {
                    margin-top: 100px !important;
                }
            }

            /* Small devices (portrait tablets and large phones, 600px and up) */
            @media only screen and (min-width: 600px) {

                .c-margin {
                    margin-top: 100px !important;
                }
            }

            /* Medium devices (landscape tablets, 768px and up) */
            @media only screen and (min-width: 768px) {

                .c-margin {
                    margin-top: 100px !important;
                }

            }

            /* Large devices (laptops/desktops, 992px and up) */
            @media only screen and (min-width: 992px) {

                .c-margin {
                    margin-top: 100px !important;
                }

            }

            /* Extra large devices (large laptops and desktops, 1200px and up) */
            @media only screen and (min-width: 1200px) {
                .c-margin {
                    margin-top: 120px !important;
                }
            }
        </style>
    @elseif(
        $host == 'localhost:8002' ||
            $host == 'primailer.com' ||
            $host == 'primailer.orkia.in' ||
            $host == '**************:4444')
        <style>
            /* Extra small devices (phones, 600px and down) */
            @media only screen and (max-width: 600px) {
                .c-margin {
                    margin-top: 100px !important;
                }
            }

            /* Small devices (portrait tablets and large phones, 600px and up) */
            @media only screen and (min-width: 600px) {

                .c-margin {
                    margin-top: 100px !important;
                }

            }

            /* Medium devices (landscape tablets, 768px and up) */
            @media only screen and (min-width: 768px) {

                .c-margin {
                    margin-top: 100px !important;
                }

            }

            /* Large devices (laptops/desktops, 992px and up) */
            @media only screen and (min-width: 992px) {

                .c-margin {
                    margin-top: 100px !important;
                }

            }

            /* Extra large devices (large laptops and desktops, 1200px and up) */
            @media only screen and (min-width: 1200px) {
                .c-margin {
                    margin-top: 120px !important;
                }
            }
        </style>
    @elseif ($host == 'localhost:8003' || $host == 'stickyfirst.com' || $host == 'stickyfirst.orkia.in')
        <style>
            .c-margin {
                margin-top: 42px;
            }
        </style>
    @elseif ($host == 'localhost:8004' || $host == 'ringcaster.com' || $host == 'ringcaster.orkia.in')
        <style>
            .c-margin {
                margin-top: 38px;
            }
        </style>
    @elseif ($host == 'localhost:8005' || $host == 'pixayogi.com' || $host == 'pixayogi.orkia.in')
        <style>
            .c-margin {
                margin-top: 67px;
            }
        </style>
    @elseif ($host == 'localhost:8006' || $host == 'rokdi.com' || $host == 'rokdi.orkia.in')
        <style>
            .c-margin {
                margin-top: 65px;
            }
        </style>
    @elseif ($host == 'localhost:8007' || $host == 'androsms.com' || $host == 'androsms.orkia.in')
        <style>
            .c-margin {
                margin-top: 37px;
            }
        </style>
    @elseif ($host == 'localhost:8008' || $host == 'clatos.com' || $host == 'clatos.orkia.in')
        <style>
            .c-margin {
                margin-top: 142px;
            }
        </style>
    @elseif ($host == 'localhost:8009' || $host == 'rapbooster.com' || $host == 'rapbooster.orkia.in')
        <style>
            .c-margin {
                margin-top: 43px;
            }
        </style>
    @elseif ($host == 'localhost:8010' || $host == 'dunesfactory.com' || $host == 'dunesfactory.orkia.in')
    @endif
    <style>
        .modal-backdrop {
            display: none !important;
        }
    </style>


    @yield('only-Page-css')
@endsection

@section('webpage')

    <div class="container-lg container-fluid p-0 mb-auto c-margin" id="main-page">

        <div class="d-flex justify-content-around gap-2">
            <div class="col-lg-3 d-none d-lg-inline-block pe-0">
                <div class="overflow-auto mb-5">
                    @include('userpages.sideMenu')
                </div>
            </div>
            <div class="col-lg-9 col-sm-12 col-md-12 p-0">
                @yield('userpagesection')

            </div>
        </div>
    </div>

    @yield('BillingProfileEditModal')


@endsection

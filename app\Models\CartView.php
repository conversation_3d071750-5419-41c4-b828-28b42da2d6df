<?php

namespace App\Models;

use App\Models\Product;
use App\Models\ProductVariantDistinct;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CartView extends Model
{
    use HasFactory;
    protected $table = "cart_view";
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id')->with('images', 'distinctVariants', 'category');
    }
    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id', 'id')->where(['isActive' => 1, 'isOnWebsite' => 1])->with('product');
    }

    public function addons()
    {
        return $this->hasMany(CartView::class, 'parentCart_id', 'cart_id')->with('variant');
    }

    public function distinctVariants()
    {
        return $this->hasMany(ProductVariantDistinct::class, 'product_id', 'product_id')->where(['isActive' => 1, 'isOnWebsite' => 1]);
    }
     public function siblingVariants()
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'product_id')->where(['isActive' => 1, 'isOnWebsite' => 1]);
    }

}

@extends('userpages.layouts.projBrandMain')
@section('title', 'Brands')

@section('proj-pages')
    <div class="card">
        <div class="card-header min-h-50px border-0">
            <div class="card-title text-dark">
                <h2 class="fs-2">Brands ({{ $brands->count() }})
                </h2>
            </div>
            <div class="card-toolbar">
                {{-- <div class="me-3">
                    @php
                        $sortUrl = '';
                        if (isset($search)) {
                            $sortUrl = request()->url() . '?' . http_build_query(['sort' => $sort, 'search' => $search]);
                        } else {
                            $sortUrl = request()->url() . '?' . http_build_query(['sort' => $sort]);
                        }
                    @endphp
                    <a aria-label="link" href="{{ $sortUrl }}" class="btn btn-sm btn-primary btn-icon h-30px w-30px " data-bs-toggle="tooltip"
                        data-bs-placement="top" data-bs-title="Sort" data-bs-custom-class="tooltip-inverse">
                        <svg xmlns="http://www.w3.org/2000/svg" height="1em" style="fill: #ffffff"
                            viewBox="0 0 320 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
                            <path
                                d="M137.4 41.4c12.5-12.5 32.8-12.5 45.3 0l128 128c9.2 9.2 11.9 22.9 6.9 34.9s-16.6 19.8-29.6 19.8H32c-12.9 0-24.6-7.8-29.6-19.8s-2.2-25.7 6.9-34.9l128-128zm0 429.3l-128-128c-9.2-9.2-11.9-22.9-6.9-34.9s16.6-19.8 29.6-19.8H288c12.9 0 24.6 7.8 29.6 19.8s2.2 25.7-6.9 34.9l-128 128c-12.5 12.5-32.8 12.5-45.3 0z" />
                        </svg>
                    </a>
                </div> --}}

                <a aria-label="link" href="{{ route('brand.create') }}" class="btn btn-success">
                    <span><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-plus-lg" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z" />
                        </svg></span>
                    Add Brand</a>
            </div>
        </div>
        <div class="card-body p-4">


            <div class="row row-gap-3">
                @forelse ($brands as $item)
                    <div class="col-sm-12 col-md-6 col-lg-4 col-xl-3 p-3  ">

                        <div class="border m-auto rounded-2   p-1  d-flex h-100">
                            <div class="d-flex gap-2">
                                @if ($item->logo)
                                    <div class=" symbol symbol-40px m-auto">
                                        <img src="{{ s3_fileShow($item->logo, 'graphic_lead_brand') }}" alt=""
                                            class="img-fluid w-100">
                                    </div>
                                @endif
                                <div class=" p-2 text-start">
                                    <div class="fs-6  text-dark  text-nowrap d-block text-truncate">
                                        {{ $item->name ?? '-' }}
                                    </div>
                                    <div class="text-gray-400   ">
                                        {{ humanDate($item->ts) }}
                                    </div>
                                </div>
                            </div>
                            <div class=" m-auto  text-end me-1">
                                <a aria-label="link" href="{{ route('brand.show', ['brand' => $item->id]) }}"
                                    class="btn btn-secondary text-nowrap  ">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        fill="currentColor" class="bi bi-gear" viewBox="0 0 16 16">
                                        <path
                                            d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z" />
                                        <path
                                            d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z" />
                                    </svg>
                                    Details
                                </a>
                            </div>
                        </div>

                    </div>
                @empty
                @endforelse

            </div>

        </div>
    </div>
@endsection

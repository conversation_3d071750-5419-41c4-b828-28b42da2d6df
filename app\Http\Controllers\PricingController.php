<?php

namespace App\Http\Controllers;

use App\Models\ProductCategory;
use Exception;

class PricingController extends Controller
{
    public function pagePricing($categoryName = null, $category)
    {
        try {
            $data['category'] = ProductCategory::where([
            'id' => $category,
            'isActive' => 1
            ])->with('products')->firstOrFail();

            return view('rapbooster.categoryPricing', $data);
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'Unable to get details');
        }
    }
}
// 57243 //

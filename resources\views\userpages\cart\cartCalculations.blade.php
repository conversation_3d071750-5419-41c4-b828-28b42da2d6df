<div class="rounded-3 mt-3 bg-white shadow-sm">
    <h4 class=" mb-0  p-3" style="background-color: #EAF1FF;border-radius:7px 7px 0 0; color: #194DAB;">Payment Details</h4>

    <div class="calculation-box  p-3 rounded-top-3">
        <div class="">
            <div class="row mb-3 fw-bold">
                <div class="col">
                    <span class="text-nowrap">MRP</span>
                    @php
                    $quantity = isset($calculation['totalItems']) ? $calculation['totalItems'] : (auth()->check() ? auth()->user()->carts()->count() : 0);
                    @endphp
                    @if ($quantity > 0)
                    <sub class="text-muted ms-1">({{ $quantity }} {{ Str::plural('Item', $quantity) }})</sub>
                    @endif
                </div>
                <div class="col text-end">₹ {{ number_format($calculation['mrp']) }}</div>
            </div>

            @foreach(['baseDiscount' => 'Discount', 'additionalDiscount' => 'Additional Discount'] as $key => $label)
            @if ($calculation[$key] > 0)
            <div class="row mb-3 fw-bold text-success">
                <div class="col text-nowrap">{{ $label }}</div>
                <div class="col text-end">-₹ {{ number_format($calculation[$key]) }}</div>
            </div>
            @endif
            @endforeach

            <hr>
            <div class="row mb-3 fw-bold">
                <div class="col text-nowrap">
                    Net Price
                    <small class="text-muted"> (Before Tax)</small>
                </div>
                <div class="col text-end">₹ {{ number_format($calculation['totalPayable'] - $calculation['totalGst']) }}</div>
            </div>
            <div class="row mb-3 fw-bold">
                <div class="col">GST</div>
                <div class="col text-end">₹ {{ number_format($calculation['totalGst']) }}</div>
            </div>
            <div class="row mb-3 fw-bold text-muted">
                <div class="col text-nowrap">Training & Support</div>
                <div class="col text-end">Included</div>
            </div>
        </div>

        <hr class="my-2">
        <div class="row p-2 bg-light rounded">
            <div class="col">
                <h5 class="mb-0">Total Payable</h5>
                <small class="text-muted">Including GST</small>
            </div>
            <div class="col text-end">
                <h5 class="mb-0">₹ {{ number_format($calculation['totalPayable']) }}</h5>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid global-header p-0">
    <div class="">
        <div class="home d-flex align-items-center gap-0  justify-content-between container-lg p-0" id="home"
            style="height: 35px; ">
            @php
                $host = request()->getHttpHost();
            @endphp
            <div class="align-items-center d-flex">
                @if ($host == 'localhost:8009' || $host == 'rapbooster.com' || $host == 'rapbooster.orkia.in')
                    <div class="align-items-center d-lg-flex d-md-flex d-none">
                        <span class="pe-2 ps-2 ">

                            <i class="fa-solid fa-phone" style="color: #525260"></i>
                        </span>
                        <a href="tel:+91-9680440022" class=" text-decoration-none fw-semibold p-0  font-sm-header"
                            style="color: #525260">Sales:
                            +91-9680440022</a>
                        &nbsp;|&nbsp;
                        <a href="tel:+91-8824799800" class=" text-decoration-none fw-semibold p-0 font-sm-header"
                            style="color: #525260">Support:
                            +91-8824799800</a>
                    </div>
                @else
                    {{-- <div class="d-flex align-items-center"> --}}
                    <a aria-label="link" class="navbar-brand" href="https://dunesfactory.com" target="_blank">
                        <img style="height: 30px;" class="m-0" src="{{ asset('assets/global/images/logo/df.png') }}"
                            alt="logo image" />
                    </a>
                    <div class="first d-flex" id="first">
                        <div class="c-dropdown float-left">
                            <button id="allProductsBtn" class="c-dropbtn border-0 mb-1" onclick="dropDownGlobal()">
                                <span class="d-flex align-items-center   gap-2"
                                    style="font-size: 16px !important; font-weight: bold;color: black;">
                                    All Products <i id="changeIconDown" class="fa-solid fa-chevron-down"></i>
                                    <i id="changeIconUp" class="fa-solid fa-chevron-up ml-2"
                                        style="display: none;"></i></span>
                            </button>
                            <div class="c-dropdown-content container-fluid position-absolute"
                                style="background-color: #fff;">
                                <div class="GH-all-product container mt-3 w-auto pb-3 p-0">
                                    @include('components.global.allproductGrid')
                                </div>
                            </div>
                        </div>
                    </div>
                    {{-- </div> --}}
                @endif
            </div>


            <div class="d-flex">
                {{-- <div>
                    <div class="getstarted">
                        <a aria-label="link" href="{{ route('help') }}" class="btn  text-dark">
                            <div>Help</div>
                        </a>
                    </div>
                </div>
                <div>
                    <div class="getstarted">
                        <a aria-label="link" href="{{ route('partner') }}" class="btn  text-dark">
                            <div>Partner with us</div>
                        </a>
                    </div>
                </div> --}}
                @if (Auth::check())
                    @if (Auth::user()->hidePrice == 0)
                        <div class="d-flex  align-items-center me-3  ">
                            <a class=" position-relative pe-2" aria-label="link" href="{{ route('u.cart') }}"
                                style="text-decoration: none;">
                                <i class="fa-solid fs-5 lh-sm fa-cart-shopping" style="color: #525260"></i>

                                <span class="position-absolute start-100 translate-middle badge rounded-pill bg-danger"
                                    style="font-size: 11px;padding: 2px 5px;top: 20%!important;">
                                    {{ auth()->user()->carts->count() }}
                                    <span class="visually-hidden">Cart Items</span>
                                </span>
                            </a>
                        </div>
                    @endif
                    <div class="dropdown">
                        <button class="border border-0 fw-semibold me-2 px-2 rounded-circle text-uppercase text-white"
                            style="background-color: #F78200;" type="button" id="dropdownMenu2"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="">{{ auth()->user()->name[0] ?? 'U' }}</div>
                        </button>
                        <div class=" dropdown-menu dropdown-menu-lg-end m-2 me-0 rounded shadow-sm w-300px pb-0"
                            aria-labelledby="dropdownMenu2" style="width: max-content !important;">
                            <li class="m-0 ">
                                <a aria-label="link" href="{{ route('user.profile') }}"
                                    class="ps-2 pe-4  py-2  dropdown-item side-menu-item {{ Route::is('user.profile') ? 'c-bg-light-blue' : 'text-secondary' }}">
                                    <div class="d-flex justify-content-between align-items-center pe-4">
                                        <div class="d-flex align-items-center fs-5 mid-gap">
                                            <span class="" style="    width: 30px;    height: 30px;">
                                                <i class="fa-solid fa-user"></i>
                                            </span>
                                            <div class="">Profile</div>
                                        </div>

                                    </div>
                                </a>
                            </li>


                            <li class="m-0 ">
                                <a aria-label="link" href="{{ route('user.address') }}"
                                    class="ps-2 pe-4  py-2  dropdown-item {{ Route::is('user.address') ? 'c-bg-light-blue' : 'text-secondary' }}">
                                    <div class="d-flex justify-content-between align-items-center pe-4">
                                        <div class="d-flex align-items-center fs-5 mid-gap">
                                            <span class="" style="    width: 30px;    height: 30px;">
                                                <i class="fa-solid fa-user-tag"></i>
                                            </span>
                                            <div class=" text-left">Billing Profile</div>
                                        </div>

                                    </div>
                                </a>
                            </li>
                            <li class="m-0 ">
                                <a aria-label="link" href="{{ route('user.s.tickets') }}"
                                    class="ps-2 pe-4 py-2  dropdown-item {{ Route::is('user.s.tickets') ? 'c-bg-light-blue' : 'text-secondary' }}">
                                    <div class="d-flex justify-content-between align-items-center pe-4">
                                        <div class="d-flex align-items-center fs-5 mid-gap">
                                            <span class="" style="    width: 30px;    height: 30px;">
                                                <i class="fa-solid fa-flag"></i>
                                            </span>
                                            <div class=" text-left">Support Tickets</div>
                                        </div>

                                    </div>
                                </a>
                            </li>
                            <li class="m-0 ">
                                <a aria-label="link" href="{{ route('user.invoices') }}"
                                    class="ps-2 pe-4 py-2 dropdown-item {{ Route::is('user.invoices') ? 'c-bg-light-blue' : 'text-secondary' }} ">
                                    <div class="d-flex justify-content-between align-items-center pe-4">
                                        <div class="d-flex align-items-center fs-5 mid-gap">
                                            <span class="" style="    width: 30px;   height: 30px;">
                                                <i class="fa-solid fa-clipboard-list fs-4"></i>
                                            </span>
                                            <div class=" text-left">Invoices</div>
                                        </div>
                                    </div>
                                </a>
                            </li>

                            <li class="m-0 ">
                                <a aria-label="link" href="{{ route('user.licences') }}"
                                    class="ps-2 pe-4 py-2 dropdown-item  {{ Route::is('user.licences') ? 'c-bg-light-blue' : 'text-secondary' }} ">
                                    <div class="d-flex justify-content-between align-items-center pe-4">
                                        <div class="d-flex align-items-center fs-5 mid-gap">
                                            <span class="" style="    width: 30px;    height: 30px;">
                                                <i class="fa-solid fa-address-card"></i>
                                            </span>
                                            <div class=" text-left">Subscriptions</div>
                                        </div>

                                    </div>
                                </a>
                            </li>

                            <li class="m-0 ">
                                <a aria-label="link" href="{{ route('user.transactions') }}"
                                    class="ps-2 pe-4 py-2 dropdown-item  {{ Route::is('user.transactions') ? 'c-bg-light-blue' : 'text-secondary' }} ">
                                    <div class="d-flex justify-content-between align-items-center pe-4 ">
                                        <div class="d-flex align-items-center fs-5 mid-gap">
                                            <span class="" style="    width: 30px;    height: 30px;">
                                                <i class="fa-brands fa-cc-mastercard"></i>
                                            </span>
                                            <div class="text-left">Transactions</div>
                                        </div>

                                    </div>
                                </a>
                            </li>
                            <div class="border border-bottom-0" style="color:#EFEFEF !important"></div>
                            <li class="m-0 mb-1">
                                <a aria-label="link" class="ps-2 pe-4 py-2 dropdown-item" href="/logout">
                                    <div class="d-flex justify-content-between align-items-center text-secondary">
                                        <div class="d-flex fs-5 mid-gap ">
                                            <span class="" style="width: 30px; height: 30px;">
                                                <i class="fa-solid fa-circle-left pt-1 fs-4"></i>
                                            </span>
                                            <div class="d-flex flex-column text-start">
                                                <div class=" text-left">Sign Out</div>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>

                        </div>
                    </div>
                @else
                    <div class="getstarted">
                        <a aria-label="link" href="{{ route('login') }}" class="btn  text-dark">
                            <div>Log In/Sign Up</div>
                        </a>
                    </div>

                @endif
            </div>
        </div>
    </div>
</div>

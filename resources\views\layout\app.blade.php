<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        @yield('title')
    </title>
    @if (app()->environment() == 'production')
        @yield('metaData')
    @endif
    @if (app()->environment() == 'local')
        <meta name="robots" content="noindex">
    @endif


    <link rel="icon" type="image/x-icon" href="@yield('siteIcon')">
    {{--
    <link rel="stylesheet" href="{{ asset('assets/global/css/style.css') }}"> --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    {{-- A<PERSON> CSS --}}
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    {{-- Font Awesome CSS --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    {{-- JQUERY --}}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    @include('components.global.accordionStyles')
    <style>
        .work-sans {
            font-family: "Work Sans", sans-serif !important;
            font-optical-sizing: auto;
        }

        .iti {
            width: 100% !important;
        }

        .whatsapp-badge {
            top: auto !important;
            left: 20px !important;
            bottom: 20px !important;
            right: auto !important;
            position: fixed !important;
            min-height: 65px !important;
            min-width: 65px !important;
            width: 64px !important;
            height: 64px !important;
            z-index: 1000001 !important;
        }
    </style>
    <style>
        .change-scroll::-webkit-scrollbar {
            width: 6.5px;
        }

        .change-scroll::-webkit-scrollbar-track {
            background: #c1c1c100;
        }

        .change-scroll::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background: #C1C1C1;
        }

        .change-scroll::-webkit-scrollbar-thumb:hover {
            background: #9f9f9f;
        }

        .change-scroll::-webkit-scrollbar-button {
            display: none;
        }
    </style>

    @yield('css')
    @yield('PAGE-CSS')
    @yield('globle-PAGE-CSS')
</head>

<body>

    @php

        $domain = allDomains()[request()->getHttpHost()];
        $product_id = $domain['product_id'];

    @endphp
    {{-- global header --}}
    @include('components.core.whatsappBadge')
    @include('components.core.header')
    @yield('breadcrumb')
    @yield('main')

    {{-- global footer --}}
    {{-- @include('components.core.footer') --}}

    <!-- Defer non-critical JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            AOS.init({
                once: true, // Whether animation should happen only once
                disable: window.innerWidth < 768 // Disable on mobile for better performance
            });
        });
    </script>

    <script src="{{ asset('assets/global/js/script.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-HwwvtgBNo3bZJJLYd8oVXjrBZt8cqVSpeBNS5n7C8IVInixGAoxmnlMuBnhbgrkm" crossorigin="anonymous">
    </script>
    @yield('script')
    @yield('PAGE-script')
    @yield('globle-PAGE-script')
    @include('components.core.getQuoteModal')
    @include('components.core.message')
    @auth
        @include('components.core.supportForm')
    @endauth

    <script>
        $('.product-variant-select').change(function(e) {

            var productSelling = $(this).find(':selected').data('productselling');
            var sellingPrice = $(this).find(':selected').data('selling');
            var minQtyDiv = $(this).find(':selected').data('productminqtydiv');
            var minQtyVal = $(this).find(':selected').data('productminqtyval');
            $("." + productSelling).text(sellingPrice);
            // $("." + minQtyDiv).text(minQtyVal);
        });

        $('.variant-select').change(function() {
            var sellingPrice = $(this).find(':selected').data('sellprice');
            var mrpPrice = $(this).find(':selected').data('mrp');
            var variantId = $(this).find(':selected').data('variant');
            var productId = $(this).find(':selected').data('product');
            var cartLink = $(this).find(':selected').data('link');
            // console.log(sellingPrice);
            // console.log(variantId);
            $('#productMrp' + productId).text('₹ ' + mrpPrice);
            $('#productSelling' + productId).text('₹ ' + sellingPrice);
            $('#productLink' + productId).attr('href', cartLink);

            // $(this).closest('.product').find('.sellingPrice').text('₹ ' + sellingPrice);
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        var swiper = new Swiper(".img-swiper", {
            pagination: {
                el: ".swiper-pagination",
            },
        });
    </script>
    <script>
        var swiper = new Swiper(".mySwiper", {
            slidesPerView: 1,
            spaceBetween: 10,
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
            breakpoints: {
                320: {
                    slidesPerView: 1.1,
                    spaceBetween: 5,
                },
                425: {
                    slidesPerView: 1.2,
                    spaceBetween: 5,
                },
                640: {
                    slidesPerView: 1.9,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 2.1,
                    spaceBetween: 15,
                },
                1024: {
                    slidesPerView: 2.6,
                    spaceBetween: 20,
                },
                1250: {
                    slidesPerView: 3,
                    spaceBetween: 20,
                },
                2040: {
                    slidesPerView: 4.1,
                    spaceBetween: 20,
                },


            },
        });
    </script>

    <script>
        $(document).ready(function() {
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(
                tooltipTriggerEl))
        });
    </script>
    <script>
        function conCountry() {
            var phoneNo = $('#number').val();
            var countryCodenumber = $('.iti__selected-dial-code').text();
            $('#number').val(countryCodenumber + phoneNo);
        }
    </script>

    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
        $(document).ready(function() {
            var Tawk_API = Tawk_API || {},
                Tawk_LoadStart = new Date();
            (function() {
                var s1 = document.createElement("script"),
                    s0 = document.getElementsByTagName("script")[0];
                s1.async = true;
                s1.src = 'https://embed.tawk.to/5d24f4f222d70e36c2a4f270/default';
                s1.charset = 'UTF-8';
                s1.setAttribute('crossorigin', '*');
                s0.parentNode.insertBefore(s1, s0);
            })();
        });
    </script>
    <!--End of Tawk.to Script-->

</body>

</html>


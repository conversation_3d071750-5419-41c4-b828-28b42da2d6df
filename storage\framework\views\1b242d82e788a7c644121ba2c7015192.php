<?php
    $host = request()->getHttpHost();
    $hostdataArray = allDomains()[$host];
    $main_path = $hostdataArray['main_path'];
    $icon = $hostdataArray['icon'];
?>
<?php $__env->startSection('siteIcon', asset($icon)); ?>

<?php $__env->startSection('title', 'Profile'); ?>
<?php $__env->startSection('PAGE-CSS'); ?>
    <style>
        #qrLoader,
        #paymentLoader {
            display: none;
        }

        .modal-backdrop {
            display: none !important;
        }

        <?php $marginMap =[ 'localhost:8001|wabhai.com|wabhai.orkia.in'=>['responsive'=>true, 'margin'=>'100px'],
        'localhost:8002|primailer.com|primailer.orkia.in|**************:4444'=>['responsive'=>true, 'margin'=>'100px'],
        'localhost:8003|stickyfirst.com|stickyfirst.orkia.in'=>['margin'=>'42px'],
        'localhost:8004|ringcaster.com|ringcaster.orkia.in'=>['margin'=>'38px'],
        'localhost:8005|pixayogi.com|pixayogi.orkia.in'=>['margin'=>'67px'],
        'localhost:8006|rokdi.com|rokdi.orkia.in'=>['margin'=>'65px'],
        'localhost:8007|androsms.com|androsms.orkia.in'=>['margin'=>'37px'],
        'localhost:8008|clatos.com|clatos.orkia.in'=>['margin'=>'142px'],
        'localhost:8009|rapbooster.com|rapbooster.orkia.in'=>['margin'=>'43px'],
        'localhost:8019|texaplus.com|texaplus.orkia.in'=>['margin'=>'43px']];

        foreach ($marginMap as $domains => $settings) {
            if (in_array($host, explode('|', $domains))) {
                if (isset($settings['responsive']) && $settings['responsive']) {
                    echo "@media only screen and (max-width: 1199px) { .c-margin { margin-top: {$settings['margin']} !important; } }";
                    echo "@media only screen and (min-width: 1200px) { .c-margin { margin-top: 120px !important; } }";
                }

                else {
                    echo ".c-margin { margin-top: {$settings['margin']}; }";
                }

                break;
            }
        }
        ?>
    </style>
    <?php echo $__env->yieldContent('only-Page-css'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('webpage'); ?>
    <div class="container container-sm-fluid p-0 mb-auto c-margin" id="main-page">
        <div class="d-flex row justify-content-around mb-5">
            <?php echo $__env->yieldContent('userpagesection'); ?>
        </div>
    </div>

    <?php echo $__env->yieldContent('BillingProfileEditModal'); ?>
    <?php echo $__env->make('components.core.message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($main_path, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\websites_laravel\resources\views/userpages/cart/main.blade.php ENDPATH**/ ?>
@php
    $isSwiper = 'swiper-wrapper';
    $swiperContainer = 'mySwiper swiper';
    if ($category && $category->products->count() < 3) {
        $isSwiper = 'd-flex no-swiper gap-3 overflow-auto';
        $swiperContainer = 'w-auto overflow-auto';
    }
@endphp

@if ($category && $category->products->count())
    <div class="container-lg">
        <div class="{{ $swiperContainer }}">
            <div class="pt-lg-3 pt-md-3 pt-4 justify-content-center {{ $isSwiper }}" style="min-width: max-content">
                @foreach ($category->products as $product)
                    @php
                        $width = $category->products->count() < 3 ? 'w-lg-50 w-md-75 w-sm-100' : 'swiper-slide';
                        $btnTextColor = '#ffffff';
                        $btnBgColor = $loop->iteration % 2 == 0 ? '#2c8cf4' : '#2D2DB0';
                    @endphp

                    @if ($product && $product->isActive)
                        <div class="h-auto mb-3 {{ $width }}">
                            <div class="bg-white d-flex flex-column h-100 justify-content-between p-4 plan-card-box rounded-4 work-sans"
                                style="z-index:3;">
                                <div class="text-center">
                                    @if (count($product->distinctVariants))
                                        <a href="{{ route('product.view', ['categoryId' => $product->category->id, 'category' => strtolower(str_replace(' ', '-', $product->category->name)), 'productVariant' => $product->distinctVariants[0]->id]) }}"
                                            class="text-decoration-none">
                                            <h4 class="fw-semibold fs-3 text-wrap text-secondary-emphasis">
                                                {{ $product->name }}
                                            </h4>
                                        </a>
                                    @else
                                        <h4 class="fw-semibold fs-3 text-wrap text-secondary-emphasis">
                                            {{ $product->name }}
                                        </h4>
                                    @endif

                                    @if (auth()->check() && !auth()->user()->hidePrice && ($product->showPriceAnonymous || $product->showPriceAfterLogin))
                                        @if ($product->distinctVariants->count())
                                            <div class="d-flex justify-content-center mb-3">
                                                <select class="variant-select form-select" style="width: max-content;">
                                                    @foreach ($product->distinctVariants as $var)
                                                        <option value="{{ $var->id }}"
                                                            data-variant="{{ $var->id }}"
                                                            data-product="{{ $product->id }}"
                                                            data-sellprice="{{ $var->sellingPrice }}"
                                                            data-mrp="{{ $var->mrp }}"
                                                            data-link="{{ route('cart.add', ['product_id' => $var->id]) }}"
                                                            @selected($loop->first)>
                                                            {{ $var->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="d-flex justify-content-center align-items-center gap-2 product">
                                                <h5 class="fw-normal fs-5 mrpPrice">
                                                    <del
                                                        id="{{ 'productMrp' . $product->id }}">₹{{ $product->distinctVariants[0]->mrp }}</del>
                                                </h5>
                                                <h4 class="fw-semibold fs-4 sellingPrice"
                                                    id="{{ 'productSelling' . $product->id }}">
                                                    ₹{{ $product->distinctVariants[0]->sellingPrice }}</h4>
                                            </div>
                                        @endif
                                    @endif
                                </div>

                                {{-- @if (auth()->user()->hidePrice != 1) --}}
                                <div class="d-flex  text-center justify-content-center flex-column gap-4">
                                    @if ($product->distinctVariants->count())
                                        @if (auth()->check() && auth()->user()->hidePrice == 0)
                                            @if ($product->showPriceAnonymous || $product->showPriceAfterLogin)
                                                <div class="p-2">
                                                    <a href="{{ route('cart.add', ['product_id' => $product->distinctVariants[0]->id]) }}"
                                                        id="{{ 'productLink' . $product->id }}"
                                                        class="btn btn-lg fw-medium text-white w-75 text-nowrap"
                                                        style="background-color: {{ $btnBgColor }}; color: {{ $btnTextColor }}">
                                                        Add to Cart
                                                    </a>
                                                </div>
                                            @endif
                                        @endif
                                    @endif

                                    @if (!auth()->check() && $product->getQuoteAnonymous)
                                        <div class="p-2">
                                            <a href="{{ route('quotation.create', ['product' => $product->id]) }}"
                                                class="btn btn-lg fw-medium text-white w-75 text-nowrap"
                                                style="background-color: {{ $btnBgColor }}; color: {{ $btnTextColor }}">Get
                                                Quotation</a>
                                        </div>
                                    @endif

                                    @if (auth()->check() && auth()->user()->hidePrice == 0 && $product->getQuoteAfterLogin)
                                        <div class="p-2">
                                            <a href="{{ route('quotation.create', ['product' => $product->id]) }}"
                                                class="btn btn-lg fw-medium text-white w-75 text-nowrap"
                                                style="background-color: {{ $btnBgColor }}; color: {{ $btnTextColor }}">Get
                                                Quotation</a>
                                        </div>
                                    @endif
                                </div>

                                <div class="d-flex flex-column mx-2 gap-2 py-3" style="font-size: 1rem;">
                                    @foreach ($product->features as $item)
                                        <div class="d-flex gap-2 align-items-baseline">
                                            <div style="width: 10%">
                                                <i class="{{ $item->iconClass ?? 'fa-solid fa-check' }}"
                                                    style="color: {{ $item->iconColorHex ?? '#2d45c1' }};"></i>
                                            </div>
                                            <div class="text-start">{{ $item->feature }}</div>
                                        </div>
                                    @endforeach
                                </div>

                                <div class="text-center">
                                    @if ($product->distinctVariants->count())
                                        <a href="{{ route('product.view', ['categoryId' => $product->category->id, 'category' => strtolower(str_replace(' ', '-', $product->category->name)), 'productVariant' => $product->distinctVariants[0]->id]) }}"
                                            class="text-decoration-none">Read More</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
            <div class="mt-5">
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </div>
@endif

@extends('components.andro.main')

@section('andro-css')
    <style>
        .bg-purple {
            background-color: #5a189a;

        }

        .output-box {
            background-color: #6a1b9a;
            color: #ffd700;
        }

        .form-range {
            background-color: transparent;
        }


        .comparison-table thead th {
            background-color: #ffffff;
            color: #e13362;
            padding-left: 30px;
        }

        .comparison-table thead tr {
            border-radius: 10px
        }

        .comparison-table tbody tr td {
            padding-left: 30px
        }

        .comparison-table {
            border-collapse: separate;
            border-spacing: 0;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: .5rem;
            overflow: hidden;
        }

        .comparison-table thead {
            background-color: #ffffff;
        }

        .comparison-table th {
            font-weight: 600;
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
        }

        .comparison-table th:first-child {
            text-align: left;
            width: 35%;
        }

        .comparison-table td {
            padding: 1rem;
            text-align: center;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }

        .comparison-table td:first-child {
            text-align: left;
            font-weight: 500;
        }

        .comparison-table tr:last-child td {
            border-bottom: none;
        }


        /* Base column borders for body cells (not headers) */
        /* For even rows (2nd, 4th, etc. → background: #f2f2f2) */
        .comparison-table tbody tr:nth-child(even) td:nth-child(n+2) {
            border-left: 2px solid #f2f2f2;
            border-right: 2px solid #f2f2f2;
        }

        /* For odd rows (1st, 3rd, etc. → background: #ffffff) */
        .comparison-table tbody tr:nth-child(odd) td:nth-child(n+2) {
            border-left: 2px solid #ffffff;
            border-right: 2px solid #ffffff;
        }


        /* On hover, override with your highlight color */
        .column-hover {
            /* background-color: #e13362 !important; */
            border-left-color: #0d6efd !important;
            border-right-color: #0d6efd !important;
        }

        /* Bottom corners for the highlighted column's last row */
        .column-hover-top {
            border-top: 2px solid #0d6efd;
            border-left: 2px solid #0d6efd;
            border-right: 2px solid #0d6efd;
            border-radius: 8px 8px 0 0;
            background-color: #e7f1ff;
            z-index: 2;
            position: relative;
        }

        .column-hover-last {
            border-bottom: 2px solid #0d6efd !important;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .column-hover,
        .column-hover-last {
            transition: background-color 0.3s ease;
        }

        @media (max-width: 768px) {

            .comparison-table td,
            .comparison-table th {
                text-align: center;
            }
        }

        .table-striped>tbody>tr:nth-of-type(odd) {
            --bs-table-striped-bg: rgb(248 247 252);
            /* Your custom stripe color */
            color: inherit;
            /* optional, keep text color normal */
        }

        /* calculator */
        .calculator {
            background: #3f1651;
            border-radius: 10px;
            padding: 2rem;
            max-width: 800px;
            margin: 3rem auto;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .highlight-box {
            background-color: #e0ffe0;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            font-size: 1.4rem;
            font-weight: bold;
            color: #2e7d32;
            margin-top: 1.5rem;
        }
    </style>
@endsection

@section('webpage')
    {{-- FIRST SECTION  --}}
    {{-- <section class="mt-5 pb-lg-5 pt-lg-4 mt-lg-0">
        <div class="container-fluid bg-light-shade rounded-5 p-t-feature w-90"
            style="background-image: url({{ asset('assets/androsms/images/h1-bg.png') }}); background-repeat: no-repeat;background-size: contain">
            <div class="container-lg d-flex flex-column align-items-center">
                <h2 class="text-pink fw-bold text-center androsms-h2"><span class="text-purple">Select a plan and</span>
                    expand your
                    business</h2>
                <p class="fs-5 text-center">Discover flexible plans for businesses of all sizes. Select the perfect solution
                    aligned with your goals, unlocking powerful features for business growth. Embrace success with
                    transparent pricing and exceptional support.</p>
                <div class="max-width-btn">

                    <div class="d-flex flex-row gap-3">
                        <a aria-label="link" class="text-decoration-none" href="{{ route('andro.contact') }}">
                            <button
                                class="rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-lg-4 ps-lg-4 pe-2 ps-2 d-flex gap-3 pink-btn-shadow pink-btn text-white">
                                <div class="d-flex fs-5">Talk to a human</div>
                                <div class="">
                                    <img style="height: 35px;" src="{{ asset('assets/global/images/button.webp') }}"
                                        alt="">
                                </div>
                            </button>
                        </a>

                        @php
                            $getstartedTarget = '/contact';
                        @endphp
                        <a aria-label="link"
                            class="text-decoration-none pink-btn text-white rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2"
                            href="{{ $getstartedTarget }}">
                            Get started
                            <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                        </a>
                    </div>

                </div>
            </div>
        </div>
    </section> --}}

    {{-- SECOND SECTION  --}}
    {{-- <div class="container-fluid pt-5 pb-5 pt-lg-4 pb-lg-0 overflow-hidden">
        <div class="container-lg pb-lg-5">
            <div class="row d-flex align-items-center flex-column-reverse flex-lg-row flex-md-row">
                <div class="col-lg-6 col-md-6 pe-lg-5 pb-md-4 pt-lg-0 pt-4">
                    <h3 class="fw-bold">It's easy to Get started</h3>
                    <p>Experience our industry-leading service at the best prices guaranteed. Unmatched value for your
                        business needs. Join thousands of satisfied customers who have elevated their success with AndroSMS.
                        Take your business to new heights with our reliable and cost-effective solutions. Trust in our
                        expertise and customer-centric approach to streamline your communication strategies and achieve
                        unparalleled results.</p>
                    <div class="max-width-btn">
                        @php

                            $getstartedTarget = '/contact';

                        @endphp
                        <a aria-label="link" class="text-decoration-none" href="{{ $getstartedTarget }}">
                            <button
                                class="pink-btn text-white rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-4 ps-4 gap-2 fs-5">
                                Get started
                                <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                            </button></a>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 text-center" data-aos="fade-left">
                    <img class="img-fluid" src="{{ asset('assets/androsms/images/plan1 1.png') }}" alt="">
                </div>
            </div>
        </div>
    </div> --}}

    {{-- Pricing-Section Start --}}
    {{-- <div class="container-fluid pb-5 mt-5  pt-3">
        <div class="container-lg mt-5">
            <h2 class="androsms-h2 text-center fw-bold pt-5"> Plans That Fit Every Budget</h2>
            <p class="text-center fs-5 mb-5">All features included in every plan. Choose your plans and enjoy the entire
                platform.</p>
            <div class="price-grid">
                <div class="price-items-1">
                    <h4 class="text-center fw-bold">Transaction SMS</h4>
                    <p class="price-para">Connect with up to <strong>5 devices</strong> for expanded reach.</p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn  price-items-1-btn px-4 py-1 text-dark">
                                    Add to cart</a>
                            @else
                                <button class="btn  price-items-1-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn  price-items-1-btn px-4 py-1 text-dark">Login to see
                                Price</a>
                        @endif
                    </div>
                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 5 devices for efficient communication.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Enjoy advanced SMS scheduling features.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Seamlessly integrate with APIs for enhanced functionality.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Set up automated replies for instant engagement.</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-1">
                    <h4 class="text-center fw-bold">Promotional SMS</h4>
                    <p class="price-para">Expand your network with support for <strong>10 devices.</strong></p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn  price-items-1-btn px-4 py-1 text-dark">Add to cart</a>
                            @else
                                <button class="btn  price-items-1-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn  price-items-1-btn px-4 py-1 text-dark">Login to see
                                Price</a>
                        @endif
                    </div>

                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 10 devices for expanded reach.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Utilize SMS scheduling for efficient messaging.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Integrate with APIs for seamless system integration.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Enable auto replies to enhance customer interaction.</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-2 mt-4 ">
                    <button class="most-popular-blue pt-2 pb-3 pe-4 ps-4 mb-3">
                        Most Popular
                    </button>
                    <h4 class="text-center fw-bold">AndroSMS Desktop</h4>
                    <p class="price-para">Reach a wider audience with support for <strong>15 devices</strong></p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn price-items-2-btn px-4 py-1 text-dark">Add to cart</a>
                            @else
                                <button class="btn price-items-2-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn price-items-2-btn px-4 py-1 text-light">Login to
                                see
                                Price</a>
                        @endif
                    </div>

                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 15 devices for wider coverage.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Access essential SMS scheduling features.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Integrate with APIs for streamlined operations.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Set up automated replies for prompt customer response.</li>
                        </div>
                    </ol>
                </div>
                <div class="price-items-3">
                    <h4 class="text-center fw-bold">AndroSMS Cloud</h4>
                    <p class="price-para">Extend your messaging capabilities with up to <strong>20 devices</strong></p>
                    <div class="text-center pb-3">
                        @if (auth()->check())
                            @if (auth()->user()->hidePrice == 0)
                                <a href="{{ route('cart.add', ['product_id' => 5]) }}"
                                    class=" btn  price-items-3-btn px-4 py-1 text-dark">Add to cart</a>
                            @else
                                <button class="btn  price-items-3-btn px-4 py-1 text-dark" data-bs-toggle="modal"
                                    data-bs-target="#getquoteModal">Get quote</button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class=" btn  price-items-3-btn px-4 py-1 text-dark">Login to
                                see Price</a>
                        @endif
                    </div>

                    <ol class="price-listing">
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Connect up to 20 devices for extensive communication.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Utilize advanced SMS scheduling options.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2 mb-3"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Seamlessly integrate with APIs for enhanced functionality.</li>
                        </div>
                        <div class="d-flex align-items-baseline gap-2"><i class="fa-solid fa-circle-check"></i>
                            <li class="price-list">Set up auto replies to improve customer engagement.</li>
                        </div>
                    </ol>
                </div>
            </div>
        </div>
    </div> --}}



    {{-- table srction  --}}
    <section class="container-fluid bg-light py-5">
        <div class="container-lg p-0">
            <h2 class="text-center mb-4 androsms-h2 fw-bold">Compare All Features</h2>
            <div class="table-responsive">
                <table class="comparison-table table bg-white table-striped fs-5">
                    <thead>
                        <tr>
                            <th class="fs-5 ">Features</th>
                            <th class="fs-5 p-1">Transaction SMS</th>
                            <th class="fs-5 p-1">Promotional SMS</th>
                            <th class="highlight-header fs-5 p-1">Google RCS API</th>
                            <th class="highlight-header fs-5 p-1">AndroSMS Desktop</th>
                            <th class="highlight-header fs-5 p-1">AndroSMS Cloud</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Per Message Cost</td>
                            <td>Credit Based</td>
                            <td>Credit Based</td>
                            <td class="highlight-column">Credit Based</td>
                            <td class="highlight-column">Free</td>
                            <td class="highlight-column">Free</td>
                        </tr>
                        <tr>
                            <td>Template Approval Required</td>
                            <td>Required</td>
                            <td>Required</td>
                            <td class="highlight-column">Required</td>
                            <td class="highlight-column">Not Required</td>
                            <td class="highlight-column">Not Required</td>
                        </tr>
                        <tr>
                            <td>Sender ID</td>
                            <td>6 Digit</td>
                            <td>6 Digit</td>
                            <td class="highlight-column">Brand Name & Number</td>
                            <td class="highlight-column">Your Number</td>
                            <td class="highlight-column">Your Number</td>
                        </tr>
                        <tr>
                            <td>Button / Interactive Messages</td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td class="highlight-column"><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                        </tr>
                        <tr>
                            <td>DLT Registration</td>
                            <td>Required</td>
                            <td>Required</td>
                            <td>Required</td>
                            <td class="highlight-column">Not Required</td>
                            <td class="highlight-column">Not Required</td>
                        </tr>

                        <tr>
                            <td>Campaign Management</td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td class="highlight-column"><i class="fas fa-check text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Delivery Reports</td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Auto Replies</td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                        </tr>
                        <tr>
                            <td>API Integration</td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                            <td><i class="fas fa-times text-danger"></i></td>
                            <td><i class="fas fa-check text-success"></i></td>
                        </tr>
                        <tr>
                            <td>Price</td>
                            <td class="fw-medium text-nowrap">Login to see prices</td>
                            <td class="fw-medium text-nowrap">Login to see prices</td>
                            <td class="fw-medium text-nowrap">Login to see prices</td>
                            <td class="fw-medium text-nowrap">Login to see prices</td>
                            <td class="fw-medium text-nowrap">Login to see prices</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>




    </section>










    {{-- calculator section  --}}
    <div class="calculator">
        <h2 class="text-center mb-4">💬 SMS Cost Saving Calculator</h2>
        <p class="text-center">
            Estimate how much you'd save yearly by using <strong>AndroSMS</strong> instead of traditional SMS charges at
            ₹0.20/message.<br>
            <strong>1 Device = 2 SIMs | 1 SIM = 100 Free SMS/day</strong>
        </p>

        <div class="mb-4">
            <label for="deviceCount" class="form-label">Number of Devices</label>
            <input type="number" class="form-control" id="deviceCount" min="1" value="10">
        </div>

        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">SIM Cards (2 per Device)</label>
                <input type="text" id="simCards" class="form-control" readonly>
            </div>
            <div class="col-md-6">
                <label class="form-label">Daily Free SMS (100 per SIM)</label>
                <input type="text" id="dailySms" class="form-control" readonly>
            </div>
            <div class="col-md-6">
                <label class="form-label">Monthly Free SMS</label>
                <input type="text" id="monthlySms" class="form-control" readonly>
            </div>
            <div class="col-md-6">
                <label class="form-label">Yearly Free SMS</label>
                <input type="text" id="yearlySms" class="form-control" readonly>
            </div>
            <div class="col-md-6">
                <label class="form-label">Traditional Monthly SMS Cost (₹0.20/SMS)</label>
                <input type="text" id="monthlyCost" class="form-control" readonly>
            </div>
            <div class="col-md-6">
                <label class="form-label">Traditional Yearly SMS Cost (₹0.20/SMS)</label>
                <input type="text" id="yearlyCost" class="form-control" readonly>
            </div>
        </div>

        <div class="highlight-box" id="savingsOutput">
            You Save ₹0 Every Year with AndroSMS
        </div>
    </div>





    {{-- Pricing-Section End --}}
    @php
        $faqs = [
            [
                'question' => 'What are the benefits of using bulk SMS?',
                'answer' =>
                    'Bulk SMS enables businesses to reach a wide audience instantly, improve customer engagement, and deliver time-sensitive information efficiently, ultimately boosting brand visibility and increasing response rates.',
            ],
            [
                'question' => 'Is opt-in necessary for bulk SMS?',
                'answer' =>
                    'Yes, compliance with regulations is vital, and recipients must provide explicit consent (opt-in) before receiving bulk SMS messages to ensure a legitimate and respectful communication channel.',
            ],
            [
                'question' => 'How are contacts managed in bulk SMS?',
                'answer' =>
                    'Contacts can be managed through the bulk SMS platform, where you can organize lists, add or remove subscribers, and segment your audience based on specific criteria for targeted messaging.',
            ],
            [
                'question' => 'Can bulk SMS be personalized?',
                'answer' =>
                    'Yes, most bulk SMS services allow personalization, enabling businesses to include individualized details like names, purchase history, or other custom information to make messages more relevant to recipients.',
            ],
            [
                'question' => 'Are there any message length restrictions in bulk SMS?',
                'answer' =>
                    'Yes, standard SMS messages are limited to 160 characters. However, some platforms allow you to send longer messages by concatenating multiple SMS into one long message.',
            ],
            [
                'question' => 'Can multimedia be included in bulk SMS?',
                'answer' =>
                    'Yes, some platforms support Multimedia Messaging Service (MMS), which allows you to send multimedia content like images, videos, audio, or vCards, enhancing the impact of your messages.',
            ],
            [
                'question' => 'How can businesses track the effectiveness of their bulk SMS campaigns?',
                'answer' =>
                    'Bulk SMS platforms provide delivery reports and analytics that show the status of each sent message, delivery rates, and click-through rates, allowing businesses to evaluate campaign performance.',
            ],
        ];
    @endphp
    <section class="container-fluid py-4 w-90 rounded-5 mb-2">
        <div class="container-lg ">
            <h3 class="fw-bold text-center androsms-h2 mb-4">Frequently Asked Questions</h3>
            <x-faq-accordion :faqs="$faqs" collapsed-color="#f9f9f9" active-color="#e13362" />
        </div>
    </section>
    @include('components.andro.earn')
@endsection

@section('andro-script')
    <script>
        // comparison table script
        document.addEventListener('DOMContentLoaded', function() {
            const table = document.querySelector('.comparison-table');
            const rows = table.querySelectorAll('tr');
            const hoverableColumns = [1, 2, 3, 4, 5]; // Only these columns are interactive

            function highlightColumn(colIndex) {
                if (!hoverableColumns.includes(colIndex)) return;

                rows.forEach((row, rowIndex) => {
                    const cells = row.querySelectorAll('th, td');
                    if (cells[colIndex]) {
                        cells[colIndex].classList.add('column-hover');
                    }

                    // Add class to the header (top cell of the column)
                    if (rowIndex === 0 && cells[colIndex]) {
                        cells[colIndex].classList.add('column-hover-top');
                    }

                    // If it's the last row and column is hoverable, add special border
                    if (rowIndex === rows.length - 1 && cells[colIndex]) {
                        cells[colIndex].classList.add('column-hover-last');
                    }
                });
            }

            function removeHighlight(colIndex) {
                if (!hoverableColumns.includes(colIndex)) return;

                rows.forEach((row, rowIndex) => {
                    const cells = row.querySelectorAll('th, td');
                    if (cells[colIndex]) {
                        cells[colIndex].classList.remove('column-hover');
                    }

                    // Remove from header
                    if (rowIndex === 0 && cells[colIndex]) {
                        cells[colIndex].classList.remove('column-hover-top');
                    }

                    if (rowIndex === rows.length - 1 && cells[colIndex]) {
                        cells[colIndex].classList.remove('column-hover-last');
                    }
                });
            }

            rows.forEach(row => {
                const cells = row.querySelectorAll('th, td');
                cells.forEach((cell, colIndex) => {
                    cell.addEventListener('mouseenter', () => highlightColumn(colIndex));
                    cell.addEventListener('mouseleave', () => removeHighlight(colIndex));
                });
            });
        });

        // calculator script
        const deviceInput = document.getElementById('deviceCount');
        const simOutput = document.getElementById('simCards');
        const dailyOutput = document.getElementById('dailySms');
        const monthlyOutput = document.getElementById('monthlySms');
        const yearlyOutput = document.getElementById('yearlySms');
        const monthlyCost = document.getElementById('monthlyCost');
        const yearlyCost = document.getElementById('yearlyCost');
        const savingsOutput = document.getElementById('savingsOutput');

        function updateSavings() {
            const devices = parseInt(deviceInput.value);
            const sims = devices * 2;
            const dailySms = sims * 100;
            const monthlySms = dailySms * 30;
            const yearlySms = dailySms * 365;
            const monthlyCharge = monthlySms * 0.2;
            const yearlyCharge = yearlySms * 0.2;

            simOutput.value = sims;
            dailyOutput.value = dailySms;
            monthlyOutput.value = monthlySms;
            yearlyOutput.value = yearlySms;
            monthlyCost.value = `₹${monthlyCharge.toLocaleString()}`;
            yearlyCost.value = `₹${yearlyCharge.toLocaleString()}`;
            savingsOutput.textContent = `You Save ₹${yearlyCharge.toLocaleString()} Every Year with AndroSMS`;
        }

        deviceInput.addEventListener('input', updateSavings);
        updateSavings();
    </script>
@endsection

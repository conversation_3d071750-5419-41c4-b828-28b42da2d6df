@extends('userpages.cart.main')

@section('title', 'Cart')
@section('PAGE-CSS')
    <link rel="stylesheet" href="{{ asset('assets/global/css/cart.css') }}">
    <style>
        .fs-14p {
            font-size: 14px;
        }

        .fs-15p {
            font-size: 15px;
        }

        .addons-box {
            background-color: #eefaff;
        }

        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #F8F7FC !important;
        }
    </style>

    <style>
        .tooltip.custom-tooltip-danger .tooltip-inner {
            background-color: #f8d7da;
            color: #842029;
            border: 1px solid #f5c2c7;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-top .tooltip-arrow::before {
            border-top-color: #f8d7da;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-bottom .tooltip-arrow::before {
            border-bottom-color: #f8d7da;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-start .tooltip-arrow::before {
            border-left-color: #f8d7da;
        }

        .tooltip.custom-tooltip-danger.bs-tooltip-end .tooltip-arrow::before {
            border-right-color: #f8d7da;
        }
    </style>
@endsection
@section('userpagesection')
    <div class="container-lg container-fluid">
        <div class="row justify-content-center mb-4">
            <div class="col-lg-8 col-sm-12 col-md-12">
                <div class="mb-3">
                    <div class="bg-white mt-3"
                        style="box-shadow: 0px 4px 7.199999809265137px 0px #00000008; border-radius:7px;">
                        <div class="d-flex align-items-center gap-3 p-3"
                            style="background-color: #EAF1FF;border-radius:7px 7px 0 0;">
                            <span class="align-items-center justify-content-center badge d-flex rounded-circle text-white"
                                style="height: 30px;width: 30px;background-color:#194DAB;">1</span>
                            <h4 class="mb-0" style="color: #194DAB;">Cart</h4>
                        </div>

                        <div class="change-scroll overflow-x-hidden cart-item-box">
                            @foreach ($cartViewData as $item)
                                <div
                                    class="p-2 p-lg-3 p-md-2 p-sm-2 @if (!$loop->last) border-bottom @endif ">

                                    <div class=" position-relative">
                                        <div class=" position-absolute end-0 top-0">
                                            <form id="deleteForm{{ $item->cart_id }}"
                                                action=" {{ route('cart.remove', ['cart' => $item->cart_id]) }}"
                                                method="POST">
                                                @csrf
                                                <input type="hidden" name="_method" value="delete">
                                                <button type="submit" class="rounded-circle btn btn-sm">
                                                    <i class="bi bi-x fs-5"></i>
                                                </button>
                                            </form>
                                        </div>
                                        <div class="row g-2 align-items-center">
                                            <div class="col-auto">
                                                <div class="rounded m-auto" style="height: 90px;width:90px">
                                                    <a target="_blank" class="text-decoration-none"
                                                        href="{{ route('product.view', ['categoryId' => $item->product->category->id, 'category' => strtolower(str_replace(' ', '-', $item->product->category->name)), 'productVariant' => $item->product_variant_id]) }}">
                                                        @if ($item->product->images->count())
                                                            <img alt="product_img" class="img-fluid rounded "
                                                                src="{{ s3_fileShow($item->product->images()->first()->image, 'product_gallery', $item->product_name) }}">
                                                        @else
                                                            <img src="https://dummyimage.com/100/969696/ffffff.jpg&text={{ $item->product_name }}"
                                                                alt="..." class="img-fluid rounded ">
                                                        @endif
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="d-flex flex-column" style="width: -webkit-fill-available;">
                                                    <a target="_blank" class="text-decoration-none"
                                                        href="{{ route('product.view', ['categoryId' => $item->product->category->id, 'category' => strtolower(str_replace(' ', '-', $item->product->category->name)), 'productVariant' => $item->product_variant_id]) }}">
                                                        <div class="align-items-end d-flex gap-2 mb-2">
                                                            <div class="mb-0 fs-5 text-dark">
                                                                {{ $item->product_name }}
                                                            </div>
                                                            <div class="mb-0 fs-6 text-secondary">
                                                                ({{ $item->product->category->name }})
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <div class="row g-2 justify-content-between">
                                                        <div class="col-auto">
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <div class="text-secondary">
                                                                    Validity
                                                                </div>
                                                                <div class="">
                                                                    <form action="{{ route('update.period') }}"
                                                                        method="POST"
                                                                        id="{{ $item->cart_id . 'VariantForm' }}">
                                                                        @csrf
                                                                        <input type="hidden" name="cart_id"
                                                                            value="{{ $item->cart_id }}">
                                                                        <select name="period"
                                                                            onchange="submitVariantForm('{{ $item->cart_id . 'VariantForm' }}')"
                                                                            class=" form-select border-secondary-subtle form-select-sm"
                                                                            style="max-width: fit-content;">
                                                                            @foreach ($item->product->distinctVariants as $variant)
                                                                                <option @selected($variant->id == $item->product_variant_id)
                                                                                    value="{{ $variant->id }}">
                                                                                    {{ $variant->name }}
                                                                                </option>
                                                                            @endforeach
                                                                        </select>
                                                                    </form>

                                                                </div>
                                                                <div class="d-flex align-items-center gap-1 updateDiv">

                                                                    @php
                                                                        $finalSiblings = $item->siblingVariants
                                                                            ->where('name', $item->variant->name)
                                                                            ->sortBy('minQty');
                                                                        $first = $finalSiblings->first();
                                                                        $last = $finalSiblings->last();
                                                                    @endphp


                                                                    {{-- @if ($item->product_quantity > $item->variant->minQty) --}}
                                                                    <button class="rounded-circle border bg-white px-1"
                                                                        data-bs-placement="top"
                                                                        onclick="changeQuantity({{ $first->minQty }},{{ $last->maxQty }},{{ $first->qtyMultiple }},'You have to select minimum {{ $first->minQty }} quantities.','{{ $item->cart_id }}','{{ $item->product_variant_id }}',-1,this)">
                                                                        <i class="fas fa-minus" style="color: #194DAB;"></i>
                                                                    </button>
                                                                    {{-- @else
                                                                        <button class="rounded-circle border bg-white px-1"
                                                                            disabled>
                                                                            <i class="fas fa-minus"
                                                                                style="color: grey;"></i>
                                                                        </button>
                                                                    @endif --}}

                                                                    <input value="{{ $item->product_quantity }}"
                                                                        data-min="{{ $first->minQty }}"
                                                                        data-max="{{ $last->maxQty }}"
                                                                        class="text-center text-dark form-control form-control-sm bg-white border-secondary-subtle"
                                                                        readonly style="width: 50px" disabled />

                                                                    {{-- @if ($item->product_quantity >= $item->variant->maxQty)
                                                                        <button class="rounded-circle border bg-white px-1"
                                                                            disabled>
                                                                            <i class="fas fa-plus" style="color: grey;"></i>
                                                                        </button>
                                                                    @else --}}
                                                                    <button class="rounded-circle border bg-white px-1"
                                                                        data-bs-placement="top"
                                                                        onclick="changeQuantity({{ $first->minQty }},{{ $last->maxQty }},{{ $first->qtyMultiple }},'You can to select Maximum {{ $last->maxQty }} quantities.','{{ $item->cart_id }}','{{ $item->product_variant_id }}',+1,this)">
                                                                        <i class="fas fa-plus" style="color: #194DAB;"></i>
                                                                    </button>
                                                                    {{-- @endif --}}

                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <div class="d-flex align-items-center gap-2  fs-15p mt-2">

                                                        @if ($item->sellingPrice < $item->mrp)
                                                            <div class="text-secondary">
                                                                ₹<del>{{ $item->mrp }}</del>
                                                            </div>
                                                            @php
                                                                $discount =
                                                                    (($item->mrp - $item->sellingPrice) / $item->mrp) *
                                                                    100;
                                                            @endphp
                                                            @if ($discount > 0)
                                                                <div class="text-success">
                                                                    {{ round($discount) }}% Off
                                                                </div>
                                                            @endif
                                                        @endif
                                                        <div class="fw-semibold">
                                                            ₹{{ $item->sellingPrice }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                    @if (count($item->addons))
                                        <div class=" border border-light-subtle addons-box rounded-0 fs-14p mt-2">
                                            <div class="border-bottom border-light-subtle p-2 fs-6 fw-semibold">
                                                Addons
                                            </div>
                                            @foreach ($item->addons as $item)
                                                <div
                                                    class="p-2 p-lg-3 p-md-2 p-sm-2 @if (!$loop->last) border-bottom border-light-subtle @endif ">
                                                    <div class=" position-relative">
                                                        <div class=" position-absolute end-0 top-0">
                                                            <form id="deleteForm{{ $item->cart_id }}"
                                                                action=" {{ route('cart.remove', ['cart' => $item->cart_id]) }}"
                                                                method="POST">
                                                                @csrf
                                                                <input type="hidden" name="_method" value="delete">
                                                                <button type="submit" class="rounded-circle btn btn-sm">
                                                                    <i class="bi bi-x fs-5"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                        <div class="row g-2 align-items-center">
                                                            <div class="col-auto">
                                                                <div class="rounded m-auto"
                                                                    style="height: 64px;width:64px">
                                                                    <a target="_blank" class="text-decoration-none"
                                                                        href="{{ route('product.view', ['categoryId' => $item->product->category->id, 'category' => strtolower(str_replace(' ', '-', $item->product->category->name)), 'productVariant' => $item->product_variant_id]) }}">
                                                                        @if ($item->product->images->count())
                                                                            <img alt="product_img"
                                                                                class="img-fluid rounded "
                                                                                src="{{ s3_fileShow($item->product->images()->first()->image, 'product_gallery', $item->product_name) }}">
                                                                        @else
                                                                            <img src="https://dummyimage.com/100/969696/ffffff.jpg&text={{ $item->product_name }}"
                                                                                alt="..." class="img-fluid rounded ">
                                                                        @endif
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            <div class="col">

                                                                <div class="d-flex flex-column"
                                                                    style="width: -webkit-fill-available;">
                                                                    <a target="_blank" class="text-decoration-none"
                                                                        href="{{ route('product.view', ['categoryId' => $item->product->category->id, 'category' => strtolower(str_replace(' ', '-', $item->product->category->name)), 'productVariant' => $item->product_variant_id]) }}">
                                                                        <div class="align-items-end d-flex gap-2 mb-2">
                                                                            <div class="mb-0 fs-5 text-dark">
                                                                                {{ $item->product_name }}
                                                                            </div>
                                                                            <div class="mb-0 fs-6 text-secondary">
                                                                                ({{ $item->product->category->name }})
                                                                            </div>
                                                                        </div>
                                                                    </a>
                                                                    <div class="row g-2 justify-content-between">
                                                                        <div class="col-auto">
                                                                            <div class="d-flex gap-2 align-items-center">
                                                                                <div class="text-secondary">
                                                                                    Validity
                                                                                </div>
                                                                                <div class="">
                                                                                    <form
                                                                                        action="{{ route('update.period') }}"
                                                                                        method="POST"
                                                                                        id="{{ $item->cart_id . 'VariantForm' }}">
                                                                                        @csrf
                                                                                        <input type="hidden"
                                                                                            name="cart_id"
                                                                                            value="{{ $item->cart_id }}">
                                                                                        <select name="period"
                                                                                            onchange="submitVariantForm('{{ $item->cart_id . 'VariantForm' }}')"
                                                                                            class=" form-select border-secondary-subtle form-select-sm"
                                                                                            style="max-width: fit-content;">
                                                                                            @foreach ($item->product->distinctVariants as $variant)
                                                                                                <option
                                                                                                    @selected($variant->id == $item->product_variant_id)
                                                                                                    value="{{ $variant->id }}">
                                                                                                    {{ $variant->name }}
                                                                                                </option>
                                                                                            @endforeach
                                                                                        </select>
                                                                                    </form>

                                                                                </div>
                                                                                <div
                                                                                    class="d-flex align-items-center gap-1 updateDiv">

                                                                                    @php
                                                                                        $finalSiblings = $item->siblingVariants
                                                                                            ->where(
                                                                                                'name',
                                                                                                $item->variant->name,
                                                                                            )
                                                                                            ->sortBy('minQty');
                                                                                        $first = $finalSiblings->first();
                                                                                        $last = $finalSiblings->last();
                                                                                    @endphp

                                                                                    <button data-bs-placement="top"
                                                                                        {{-- title= --}}
                                                                                        class="rounded-circle border bg-white px-1"
                                                                                        onclick="changeQuantity({{ $first->minQty }},{{ $last->maxQty }},{{ $first->qtyMultiple }},'You have to select minimum {{ $first->minQty }} quantities.','{{ $item->cart_id }}','{{ $item->product_variant_id }}',-1,this)">
                                                                                        <i class="fas fa-minus"
                                                                                            style="color: #194DAB;"></i>
                                                                                    </button>


                                                                                    <input data-min="{{ $first->minQty }}"
                                                                                        data-max="{{ $last->maxQty }}"
                                                                                        value="{{ $item->product_quantity }}"
                                                                                        class="text-center text-dark form-control form-control-sm bg-white border-secondary-subtle"
                                                                                        readonly style="width: 50px"
                                                                                        disabled />

                                                                                    <button data-bs-placement="top"
                                                                                        class="rounded-circle border bg-white px-1"
                                                                                        onclick="changeQuantity({{ $first->minQty }},{{ $last->maxQty }},{{ $first->qtyMultiple }},'You can select Maximum {{ $last->maxQty }} quantities.','{{ $item->cart_id }}','{{ $item->product_variant_id }}',+1,this)">
                                                                                        <i class="fas fa-plus"
                                                                                            style="color: #194DAB;"></i>
                                                                                    </button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-4">
                                                                            <div
                                                                                class="d-flex align-items-center gap-2 justify-content-end fs-15p">

                                                                                @if ($item->sellingPrice < $item->mrp)
                                                                                    <div class="text-secondary">
                                                                                        ₹<del>{{ $item->mrp }}</del>
                                                                                    </div>
                                                                                    @php
                                                                                        $discount =
                                                                                            (($item->mrp -
                                                                                                $item->sellingPrice) /
                                                                                                $item->mrp) *
                                                                                            100;
                                                                                    @endphp
                                                                                    @if ($discount > 0)
                                                                                        <div class="text-success">
                                                                                            {{ round($discount) }}% Off
                                                                                        </div>
                                                                                    @endif
                                                                                @endif
                                                                                <div class="fw-semibold">
                                                                                    ₹{{ $item->sellingPrice }}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            @endforeach

                                        </div>
                                    @endif
                                </div>
                            @endforeach

                        </div>
                        @if ($cartViewData->count() != 0)
                            <hr class="m-0">
                            <div class="text-end p-3">
                                <a aria-label="link" href="{{ route('checkout') }}"
                                    class="btn p-2 ps-3 pe-3 rounded-pill text-white" style="background-color: #194DAB;">
                                    Checkout
                                    <i class="fa-solid fa-chevron-right"></i>
                                </a>
                            </div>
                        @else
                            <div class="m-auto text-center p-4">
                                <div class="w-lg-25 w-sm-100 text-center m-auto">
                                    <img src="{{ asset('media/images/catbox.png') }}" alt="" class="img-fluid">
                                </div>
                                <h2 class="f-2" style="color: #df5959">Empty Cart !</h2>
                                <div class="text-end p-3">
                                    <a aria-label="link" href="/" class="btn p-2 ps-3 pe-3 rounded-pill text-white"
                                        style="background-color: #194DAB;">
                                        Continue Browsing
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        @endif

                    </div>
                </div>

            </div>
            @if ($cartViewData->count() != 0)
                <div class="col-lg-4 col-md-12 col-sm-12 ">
                    @include('userpages.cart.cartCalculations')
                    {{-- @if ($cartCoupons->count() != 0)
                    @include('userpages.invoice.coupon')
                    @endif --}}
                </div>
            @endif
        </div>
    </div>
@endsection
@section('PAGE-script')


    <script>
        function changeQuantity(min, max, step, ttText, cid, pid, type, e) {
            const $btn = $(e);
            $btn.attr('disabled', true);
            const tooltipText = ttText; // Set your custom tooltip message

            // Your quantity update logic
            const quantityInput = $btn.closest('.updateDiv').find("input");
            const updatedVal = parseInt(quantityInput.val()) + type;

            const quantityInputVal = Number(quantityInput.val());

            // console.log(min, max, quantityInputVal, type);
            let showTooltip = 0
            let newQty = 0;
            if (type == '1') {
                if (quantityInput.val() >= max) {
                    showTooltip = 1
                }

                newQty = quantityInputVal + step
            } else if (type == '-1') {
                if (quantityInput.val() <= min) {
                    showTooltip = 1
                }
                newQty = quantityInputVal - step
            }
            if (showTooltip == 1) {

                if (!$btn.data('bs.tooltip')) {
                    new bootstrap.Tooltip($btn[0], {
                        trigger: 'manual',
                        placement: 'top',
                        title: tooltipText,
                        customClass: 'custom-tooltip-danger'
                    });
                }

                $btn.tooltip('show');
                setTimeout(() => {
                    $btn.tooltip('hide');
                    $btn.attr('disabled', false);

                }, 3000);


            } else {
                quantityInput.val(newQty);
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
                $.ajax({
                    type: 'POST',
                    url: '/update/cart',
                    data: {
                        'cid': cid,
                        'pid': pid,
                        'type': type
                    },
                    dataType: 'json',
                    success: function(data) {
                        window.location.reload()
                    },
                    error: function(data) {
                        console.log(data);
                    }
                });
            }

        }

        function submitVariantForm(formID) {
            $('#' + formID).submit();
        }
    </script>
@endsection

@extends('components.andro.main')

@section('andro-css')
    <style>
        .card-0 {
            color: #fff;
            background-color: #EAE7FA;
            position: relative;
            margin-left: 180px;
            height: 280px;
        }

        .carousel-indicators li {
            cursor: pointer;
            border-radius: 50% !important;
            width: 10px;
            height: 10px;
        }

        .profile {
            color: #000;
            background-color: #FFF5C5;
            position: absolute;
            left: -179px;
            top: 22%;
        }

        .profile-pic {
            width: 120px;
            height: 120px;
        }

        .open-quotes {
            margin-left: 145px;
            margin-top: 55px;
            color: #000;
            min-width: 30px;
            min-height: 30px;
        }

        .content {
            margin-left: 150px;
            margin-right: 80px;
            color: #000;
            height: 142px;
        }

        .close-quotes {
            margin-bottom: 48px;
            margin-right: 100px;
            color: #000;
        }

        .bg-pink {
            background-color: #ff69b4;
        }

        .rotated {
            transform: rotate(180deg);
        }

        .androsms-h2 {
            font-size: 2.5rem;
            color: #333;
        }

        /* Responsive styles for tablets and below (1024px and below) */
        @media screen and (max-width: 1024px) {
            .card-0 {
                margin-left: 0;
                margin-bottom: 120px;
                max-height: 275px;
            }


            .profile {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                top: -85px;
                z-index: 10;
            }

            .carousel-control-next,
            .carousel-control-prev {
                top: -98px
            }

            .open-quotes {
                margin-left: 50px;
                margin-top: 35px;
            }

            .content {
                margin-left: 50px;
                margin-right: 50px;
                margin-top: 20px;
            }

            .close-quotes {
                margin-right: 50px;
                margin-bottom: 30px;
            }

            .carousel-inner {
                overflow: visible;
            }
        }

        /* Mobile styles */
        @media screen and (max-width: 768px) {
            .card-0 {
                margin-left: 10px;
                margin-right: 10px;
                margin-bottom: 100px;
            }

            .profile {
                left: 50%;
                transform: translateX(-50%);
                top: -70px;
            }

            .content {
                margin-right: 40px;
                margin-left: 40px;
            }

            .open-quotes {
                margin-left: 40px;
                margin-top: 14px;
            }

            .close-quotes {
                margin-bottom: 40px;
                margin-right: 40px;
            }

            .profile-pic {
                width: 100px;
                height: 100px;
            }
        }

        @media screen and (max-width: 425px) {
            .profile {
                left: 50%;
                transform: translateX(-50%);
                top: -60px;
                width: 300px;
            }

            .content {
                margin-left: 20px;
                margin-right: 20px;
                margin-top: 0px;
                overflow-y: scroll;
            }

            .open-quotes {
                margin-left: 20px;
                margin-top: 70px;
            }

            .close-quotes {
                margin-right: 20px;
                margin-bottom: 30px;
            }

            .profile-pic {
                width: 90px;
                height: 90px;
            }

            .card-0 {
                max-height: 332px;
            }
        }

        @media screen and (max-width: 375px) {
            .androsms-h2 {
                font-size: 2rem;
            }
        }

        @media screen and (max-width: 320px) {
            .profile {
                width: 270px;
            }

            .content {
                padding-left: 10px !important;
                padding-right: 10px !important;
                font-size: 18px !important;
            }

            .profile-pic {
                width: 80px;
                height: 80px;
            }

            .androsms-h2 {
                font-size: 1.8rem;
            }
        }

        @media screen and (max-width: 600px) {
            .card-main {
                padding: 50px 10px;
            }
        }
    </style>
@endsection

@section('andro-script')
    <script>
        var counters = document.querySelectorAll('.counter-count');
        var isCountingStarted = false;

        var startCountAnimation = function() {
            if (!isCountingStarted) {
                counters.forEach(function(counter) {
                    var duration = 4000; // The duration (in milliseconds) for the count up animation
                    var startCount = 0; // The starting count
                    var targetCount = parseInt(counter.innerText); // The target count

                    var increment = targetCount / duration *
                        10; // Adjust the division value to change the animation speed
                    var currentCount = startCount;

                    var updateCount = function() {
                        if (currentCount <= targetCount) {
                            var formattedCount = Math.ceil(currentCount);
                            counter.innerHTML = formattedCount + counter.innerHTML.substring(formattedCount
                                .toString().length);
                            currentCount += increment;
                            setTimeout(updateCount, 10);
                        } else {
                            counter.textContent = targetCount + counter.innerHTML.substring(targetCount
                                .toString().length);
                        }
                    };

                    updateCount();
                });

                isCountingStarted = true;
            }
        };

        var observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    startCountAnimation();
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1
        });

        var section = document.querySelector('.count-up');
        observer.observe(section);
    </script>
@endsection

@section('webpage')
    {{-- 1. HERO SECTION  --}}
    <div class="container-fluid pt-5 mt-4">
        <div class="container-lg mt-5">
            <div class="">
                <div class="flex flex-column align-items-center mb-4 mb-lg-0 ">
                    <h1 class="display-4 fw-bold text-center text-purple">Reach, Engage & Convert With Powerful Messaging
                        Solutions</h1>
                    <p class="fs-4 my-4 text-center">
                        Engage customers with rich, real-time messaging through RCS and SMS.
                        Automate at scale, personalize with ease, and drive real results.
                    </p>
                    <a aria-label="link"
                        class="text-decoration-none flex-row rounded-pill border-0 d-flex align-items-center pt-2 pb-2 pe-lg-4 ps-lg-4 pe-3 ps-3 d-flex gap-3 pink-btn-shadow pink-btn text-white"
                        href="{{ route('andro.contact') }}">
                        <div class="d-flex f-s-18 ">Talk to a human</div>
                        <div class="">
                            <img style="height: 35px;"src="{{ asset('assets/global/images/button.webp') }}" alt="">
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <div class="">
            <img class="img-fluid" src="{{ asset('assets/andro/images/Topimage.svg') }}" alt="Hero image">
        </div>
    </div>

  

    <section class="container-fluid pt-5" style="background-color: #F2F5FF">
        <div class="container-lg py-5">
            <div class="">
                <div class="flex flex-column align-items-center mb-5 ">
                    <h1 class="display-5 fw-bold text-center">One Platform. Two Powerful Channels.</h1>
                </div>
            </div>
            <div class="">
                <img class="img-fluid" src="{{ asset('assets/andro/images/SMSRCS.svg') }}" alt="Hero image">
            </div>
        </div>
    </section>

  {{-- analytics section --}}

    <section class="container-fluid py-5 my-5">
        <div class="container-lg">
            <h1 class="display-5 fw-bold text-center">One Platform. Two Powerful Channels.</h1>
            <p class="fs-5 mb-4 text-center text-secondary">Turn traditional messaging into a visual, interactive journey.
                RCS lets your brand
                stand out in the inbox .</p>
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="card border-0">
                        <div class="card-body">
                            <div class="row g-0 align-items-center text-center count-up">
                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="p-3">
                                        <div class="display-4 fw-bold counter-count text-pink">75%</div>
                                        <div class="fs-5 text-secondary">Higher Interaction Rate</div>
                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="p-3">
                                        <div class="display-4 fw-bold counter-count text-pink">4X</div>
                                        <div class="fs-5 text-secondary">Increase Conversion Rate</div>
                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="p-3">
                                        <div class="display-4 fw-bold counter-count text-pink">14+</div>
                                        <div class="fs-5 text-secondary">Years of Industry Experience</div>
                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-6 col-12">
                                    <div class="p-3">
                                        <div class="display-4 fw-bold counter-count text-pink">85%</div>
                                        <div class="fs-5 text-secondary">Higher ROI</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- left side image section  --}}
    <div class="container-fluid py-5 overflow-hidden">
        <div class="container-lg">
            <div class="row align-items-center gy-5">
                <div class="col-12 col-lg-6 text-center" data-aos="fade-right">
                    <img class="img-fluid px-3 px-sm-4" src="{{ asset('assets/andro/images/Convert leads.svg') }}"
                        alt="RCS detailed">
                </div>
                <div class="col-12 col-lg-6 px-3 px-sm-4">
                    <h3 class="androsms-h2 fw-bold mb-4" style="color: #212529">RCS Messaging That Goes Beyond Text</h3>
                    <p class="fs-5 mb-4">Turn traditional messaging into a visual, interactive journey. RCS lets your brand
                        stand out in the inbox and guide customers through actions seamlessly.</p>

                    <div class="row gy-3 mt-2 mb-4">
                        <div class="col-12 col-md-6">
                            <div class="d-flex gap-3 align-items-start">
                                <div class="text-success flex-shrink-0">
                                    <i class="fa-solid fa-check-circle fa-lg fs-5"></i>
                                </div>
                                <div class="fs-5">Elevate brand trust with verified sender identity</div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="d-flex gap-3 align-items-start">
                                <div class="text-success flex-shrink-0">
                                    <i class="fa-solid fa-check-circle fa-lg fs-5"></i>
                                </div>
                                <div class="fs-5">Drive higher click-throughs with interactive buttons and carousels</div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="d-flex gap-3 align-items-start">
                                <div class="text-success flex-shrink-0">
                                    <i class="fa-solid fa-check-circle fa-lg fs-5"></i>
                                </div>
                                <div class="fs-5">Deliver immersive product previews through images and videos</div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="d-flex gap-3 align-items-start">
                                <div class="text-success flex-shrink-0">
                                    <i class="fa-solid fa-check-circle fa-lg fs-5"></i>
                                </div>
                                <div class="fs-5">Streamline customer journeys with in-chat payments and scheduling</div>
                            </div>
                        </div>
                    </div>

                    @php
                        $getstartedTarget = Auth::check() ? route('andro.pricing') : route('login');
                    @endphp
                    <a aria-label="link"
                        class="text-decoration-none pink-btn text-white rounded-pill border-0 d-inline-flex align-items-center py-2 px-4 gap-2"
                        href="{{ $getstartedTarget }}">
                        Get started
                        <i class="fa-brands fa-telegram fa-2x" style="color: #FFF;"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    {{-- cards section --}}

    <section class="container-fluid container-lg py-5 my-5">

        <div class="text-center">
            <h3 class="androsms-h2 fw-bold mb-4">All-in-One Messaging Solution</h3>
            <p class="fs-5 mb-4">From marketing and promotions to real time customer support, boost interactions with
                SMS
                and RCS messaging.</p>
        </div>
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 w-100">
            <!-- Automated Marketing card -->
            <div class="col">
                <div class="card h-100 border-0 rounded-4 overflow-hidden">
                    <div class="card-body p-3">
                        <h5 class="card-title fs-4 mb-4 w-90 m-auto ms-4">Marketing</h5>
                        <div class="text-center mb-4 rounded-5 pt-4 border-3 w-90 m-auto"
                            style="background-color: #CDE6DB">
                            <img src="{{ asset('assets/andro/images/automatedm.svg') }}" class="img-fluid rounded-3"
                                alt="Automated Marketing screenshot">
                        </div>
                        <p class="card-text fs-5 small ms-4">Boost customer engagement and drive conversions through
                            personalized SMS and RCS marketing campaigns.</p>
                    </div>
                </div>
            </div>

            <!-- promotional card -->
            <div class="col">
                <div class="card h-100 border-0 rounded-4 overflow-hidden">
                    <div class="card-body p-3">
                        <h5 class="card-title fs-4 mb-4 w-90 m-auto ms-4">Promotions</h5>
                        <div class="text-center mb-4 bg-info-subtle rounded-5 pt-4 w-90 m-auto">
                            <img src="{{ asset('assets/andro/images/promotional-card.svg') }}" class="img-fluid rounded-3"
                                alt="promotional sms screenshot">
                        </div>
                        <p class="card-text fs-5 small ms-4">Make sure your customers never miss an update, all delivered
                            through secure and reliable messaging.</p>
                    </div>
                </div>
            </div>

            <!-- Customer care card -->
            <div class="col">
                <div class="card h-100 border-0 rounded-4 overflow-hidden">
                    <div class="card-body p-3">
                        <h5 class="card-title fs-4 mb-4 w-90 m-auto ms-4">Customer Engagements</h5>
                        <div class="text-center mb-4 bg-faq rounded-5 pt-4 w-90 m-auto">
                            <img src="{{ asset('assets/andro/images/customer engagements.svg') }}"
                                class="img-fluid rounded-3" alt="Customer care screenshot">
                        </div>
                        <p class="card-text fs-5 small ms-4">Offer real-time assistance and post-sale support through SMS
                            and
                            RCS chats all within the messaging app.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- right side image  --}}
    <section class="container-fluid py-4 py-lg-5 my-3 my-lg-5 overflow-hidden">
        <div class="container-lg">
            <div class="row align-items-center gy-4 gy-lg-5">
                <div class="col-12 col-lg-6 px-3 px-lg-4">
                    <h3 class="fw-bold mb-3 mb-lg-4 androsms-h2">SMS Marketing That Actually Converts</h3>
                    <p class="fs-5">Reach your customers where it matters — their phones. With SMS marketing, send
                        timely
                        updates, promotions, OTPs, and more. Fast, personal, and effective communication that drives
                        results.
                    </p>

                    <div class="row gy-3 mt-3 mt-lg-4 mb-2">
                        <div class="col-12 col-sm-6">
                            <div class="d-flex gap-2 gap-lg-3 align-items-start">
                                <div class="text-success flex-shrink-0 pt-1">
                                    <i class="fa-solid fa-check-circle fa-lg"></i>
                                </div>
                                <div class="fs-5">Reduce operational costs with intelligent automation</div>
                            </div>
                            <div class="d-flex gap-2 gap-lg-3 align-items-start mt-3">
                                <div class="text-success flex-shrink-0 pt-1">
                                    <i class="fa-solid fa-check-circle fa-lg"></i>
                                </div>
                                <div class="fs-5">Improve customer satisfaction with real-time updates</div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6">
                            <div class="d-flex gap-2 gap-lg-3 align-items-start">
                                <div class="text-success flex-shrink-0 pt-1">
                                    <i class="fa-solid fa-check-circle fa-lg"></i>
                                </div>
                                <div class="fs-5">Enable effortless confirmations or rescheduling</div>
                            </div>
                            <div class="d-flex gap-2 gap-lg-3 align-items-start mt-3">
                                <div class="text-success flex-shrink-0 pt-1">
                                    <i class="fa-solid fa-check-circle fa-lg"></i>
                                </div>
                                <div class="fs-5">Create memorable experiences with rich media</div>
                            </div>
                        </div>
                    </div>

                    @php
                        $getstartedTarget = Auth::check() ? route('andro.pricing') : route('login');
                    @endphp
                    <a aria-label="link"
                        class="text-decoration-none pink-btn text-white rounded-pill border-0 d-inline-flex align-items-center py-2 px-4 gap-2 mt-3 mt-lg-4"
                        href="{{ $getstartedTarget }}">
                        Get Started
                        <i class="fa-brands fa-telegram" style="color: #FFF; font-size: 1.5em;"></i>
                    </a>
                </div>
                <div class="col-12 col-lg-6">
                    <div class="text-center px-2 px-lg-4" data-aos="fade-left">
                        <img class="img-fluid" src="{{ asset('assets/andro/images/sms marketing.png') }}"
                            alt="Global messaging map">
                    </div>
                </div>
            </div>
        </div>
    </section>


    <div class="container-fluid py-5 bg-white">
        <div class="container-lg">
            <div class="row g-5">
                <!-- Left side with image -->
                <div class="col-lg-6 col-md-12 position-relative mb-4 mb-lg-0">
                    <div class="position-relative" data-aos="fade-right">
                        <img src="{{ asset('assets/andro/images/whyandro.svg') }}" alt="Team photo" class="img-fluid">
                        <div class="position-absolute d-none d-lg-block"
                            style="bottom: -20px; left: -20px; width: 25%; height: 40%; background-color: #ffcc33; z-index: -1;">
                        </div>
                    </div>
                </div>

                <!-- Right side with content -->
                <div class="col-lg-6 col-md-12 ps-lg-5 ps-md-3 ps-sm-2">
                    <h2 class="fw-bold mb-4 androsms-h2">Why AndroSMS?</h2>
                    <p class="mb-4 fs-5">Discover the benefits of working with us, where technology meets trust.</p>

                    <!-- Feature 1 -->
                    <div class="d-flex mb-4 align-items-start">
                        <div class="me-3">
                            <div class="rounded-circle d-flex justify-content-center align-items-center"
                                style="width: 50px; height: 50px; background-color: #ff5f45; color: white; font-weight: bold; font-size: 1.5rem;">
                                1</div>
                        </div>
                        <div class="mt-1">
                            <h3 class="fw-bold mb-2 fs-4">Dedicated and Responsive Support</h3>
                            <p class="fs-5">Our dedicated team offers real-time, human support—no bots, no outsourcing.
                                Whether you're launching a campaign or need quick assistance, we respond quickly to keep
                                your business
                                moving.</p>
                        </div>
                    </div>

                    <!-- Feature 2 -->
                    <div class="d-flex mb-4 align-items-start">
                        <div class="me-3">
                            <div class="rounded-circle d-flex justify-content-center align-items-center"
                                style="width: 50px; height: 50px; background-color: #45ccff; color: white; font-weight: bold; font-size: 1.5rem;">
                                2</div>
                        </div>
                        <div class="mt-1">
                            <h3 class="fw-bold mb-2 fs-4">Smooth Set up & Registrations</h3>
                            <p class="fs-5">With AndroSMS, getting started is simple and efficient. Our platform is
                                designed for ease of use, allowing your team to launch SMS and RCS campaigns quickly without
                                technical
                                bottlenecks. No long contracts, complex integrations, or drawn-out setup processes—just a
                                smooth, guided start.</p>
                        </div>
                    </div>

                    <!-- Feature 3 -->
                    <div class="d-flex mb-4 align-items-start">
                        <div class="me-3">
                            <div class="rounded-circle d-flex justify-content-center align-items-center"
                                style="width: 50px; height: 50px; background-color: #ffcc33; color: white; font-weight: bold; font-size: 1.5rem;">
                                3</div>
                        </div>
                        <div class="mt-1">
                            <h3 class="fw-bold mb-2 fs-4">Honest, Transparent Pricing</h3>
                            <p class="fs-5">We offer clear, upfront pricing with no hidden charges or unexpected fees.
                                Whether you're scaling up or sending targeted campaigns, you'll always know what you're
                                paying for. Our
                                pricing model is designed to give businesses control, flexibility, and full transparency.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- testimonials section --}}
    {{-- <section class="container-fluid container-lg py-5 mt-4">
        <div class="">
            <h2 class="text-center fw-bold mb-4 mb-lg-0 androsms-h2">Customer Feedback?<br>Of Course...
            </h2>
            <div class="row justify-content-center">
                <div class="col-lg-11">
                    <div id="testimonialCarousel" class="carousel slide mt-sm-5" data-bs-ride="carousel">
                        <div class="carousel-inner mt-4">
                            <!-- First Testimonial -->
                            <div class="carousel-item active">
                                <div class="card border-0 card-0 rounded-4">
                                    <div class="card profile py-3 px-4 flex-row gap-3 rounded-4 ">
                                        <div class="text-center">
                                            <img src="https://i.imgur.com/gazoShk.jpg"
                                                class="img-fluid profile-pic rounded-circle">
                                        </div>
                                        <div class="flex flex-column justify-content-center">
                                            <h6 class="mb-0 mt-2">Marielle Haag</h6>
                                            <small>Backend Developer</small>
                                            <div class="text-warning mb-2">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote open-quotes" viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                    <p class="content mb-0 px-5 fs-5">Excepteur sint occaecat cupidatat non proident, sunt
                                        in culpa
                                        qui officia deserunt mollit anim id est laborum. Excepteur sint occaecat cupidatat
                                        non proident, sunt
                                        in culpa
                                        qui officia deserunt mollit anim id est laborum.Excepteur sint occaecat cupidatat
                                        non proident, sunt
                                        qui officia
                                    </p>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote close-quotes ms-auto rotated "
                                        viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                </div>
                            </div>
                            <!-- Second Testimonial -->
                            <div class="carousel-item">
                                <div class="card border-0 card-0 rounded-4">
                                    <div class="card profile py-3 px-4 flex-row gap-3 rounded-4 ">
                                        <div class="text-center">
                                            <img src="https://i.imgur.com/gazoShk.jpg"
                                                class="img-fluid profile-pic rounded-circle">
                                        </div>
                                        <div class="flex flex-column justify-content-center">
                                            <h6 class="mb-0 mt-2">Marielle Haag</h6>
                                            <small>Backend Developer</small>
                                            <div class="text-warning mb-2">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote open-quotes" viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                    <p class="content mb-0 px-5 fs-4">Excepteur sint occaecat cupidatat non proident, sunt
                                        in culpa
                                        qui officia deserunt mollit anim id est laborum.</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote close-quotes ms-auto rotated "
                                        viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                </div>
                            </div>
                            <!-- Third Testimonial -->
                            <div class="carousel-item">
                                <div class="card border-0 card-0 rounded-4">
                                    <div class="card profile py-3 px-4 flex-row gap-3 rounded-4 ">
                                        <div class="text-center">
                                            <img src="https://i.imgur.com/gazoShk.jpg"
                                                class="img-fluid profile-pic rounded-circle">
                                        </div>
                                        <div class="flex flex-column justify-content-center">
                                            <h6 class="mb-0 mt-2">Marielle Haag</h6>
                                            <small>Backend Developer</small>
                                            <div class="text-warning mb-2">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote open-quotes" viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                    <p class="content mb-0 px-5 fs-4">Excepteur sint occaecat cupidatat non proident, sunt
                                        in culpa
                                        qui officia deserunt mollit anim id est laborum.</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote close-quotes ms-auto rotated "
                                        viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                </div>
                            </div>
                            <!-- Carousel Controls -->
                            <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel"
                                data-bs-slide="prev">
                                <span class="carousel-control-prev-icon bg-pink rounded-circle p-3"
                                    aria-hidden="true"></span>
                                <span class="visually-hidden">Previous</span>
                            </button>
                            <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel"
                                data-bs-slide="next">
                                <span class="carousel-control-next-icon bg-pink rounded-circle p-3"
                                    aria-hidden="true"></span>
                                <span class="visually-hidden">Next</span>
                            </button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section> --}}
    <section class="container-fluid container-lg py-5 mt-4 overflow-hidden">
        <div class="">
            <h2 class="text-center fw-bold mb-5 androsms-h2">Customer Feedback?<br>Of Course...
            </h2>
            <div class="row justify-content-center">
                <div class="col-lg-11">
                    <div id="testimonialCarousel" class="carousel slide mt-sm-5" data-bs-ride="carousel">
                        <div class="carousel-inner mt-4">
                            <!-- First Testimonial -->
                            <div class="carousel-item active">
                                <div class="card border-0 card-0 rounded-4">
                                    <div class="card profile py-3 px-4 flex-row gap-3 rounded-4 ">
                                        <div class="text-center">
                                            <img src="https://i.imgur.com/gazoShk.jpg"
                                                class="img-fluid profile-pic rounded-circle">
                                        </div>
                                        <div class="flex flex-column justify-content-center">
                                            <h6 class="mb-0 mt-2">Marielle Haag</h6>
                                            <small>Backend Developer</small>
                                            <div class="text-warning mb-2">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote open-quotes" viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                    <p class="content mb-0 px-5 fs-5">Excepteur sint occaecat cupidatat non proident, sunt
                                        in culpa
                                        qui officia deserunt mollit anim id est laborum. Excepteur sint occaecat cupidatat
                                        non proident, sunt
                                        in culpa
                                        qui officia deserunt mollit anim id est laborum. Excepteur sint occaecat cupidatat
                                        non proident, sunt
                                        qui officia
                                    </p>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote close-quotes ms-auto rotated"
                                        viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                </div>
                            </div>
                            <!-- Second Testimonial -->
                            <div class="carousel-item">
                                <div class="card border-0 card-0 rounded-4">
                                    <div class="card profile py-3 px-4 flex-row gap-3 rounded-4 ">
                                        <div class="text-center">
                                            <img src="https://i.imgur.com/gazoShk.jpg"
                                                class="img-fluid profile-pic rounded-circle">
                                        </div>
                                        <div class="flex flex-column justify-content-center">
                                            <h6 class="mb-0 mt-2">John Doe</h6>
                                            <small>Frontend Developer</small>
                                            <div class="text-warning mb-2">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote open-quotes" viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                    <p class="content mb-0 px-5 fs-5">Amazing experience working with this team. Highly
                                        professional and delivered exactly what we needed on time!</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote close-quotes ms-auto rotated "
                                        viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                </div>
                            </div>
                            <!-- Third Testimonial -->
                            <div class="carousel-item">
                                <div class="card border-0 card-0 rounded-4">
                                    <div class="card profile py-3 px-4 flex-row gap-3 rounded-4 ">
                                        <div class="text-center">
                                            <img src="https://i.imgur.com/gazoShk.jpg"
                                                class="img-fluid profile-pic rounded-circle">
                                        </div>
                                        <div class="flex flex-column justify-content-center">
                                            <h6 class="mb-0 mt-2">Sarah Wilson</h6>
                                            <small>UI/UX Designer</small>
                                            <div class="text-warning mb-2">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote open-quotes" viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                    <p class="content mb-0 px-5 fs-5">Outstanding design quality and attention to detail.
                                        The user experience is absolutely perfect!</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                                        fill="currentColor" class="bi bi-quote close-quotes ms-auto rotated "
                                        viewBox="0 0 16 16">
                                        <path
                                            d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388q0-.527.062-1.054.093-.558.31-.992t.559-.683q.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 9 7.558V11a1 1 0 0 0 1 1zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612q0-.527.062-1.054.094-.558.31-.992.217-.434.559-.683.34-.279.868-.279V3q-.868 0-1.52.372a3.3 3.3 0 0 0-1.085.992 4.9 4.9 0 0 0-.62 1.458A7.7 7.7 0 0 0 3 7.558V11a1 1 0 0 0 1 1z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Carousel Controls -->
                        <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel"
                            data-bs-slide="prev">
                            <span class="carousel-control-prev-icon rounded-circle p-3" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel"
                            data-bs-slide="next">
                            <span class="carousel-control-next-icon rounded-circle p-3" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- sixth FAQ SECTION --}}
    @php
        $faqs = [
            [
                'question' => 'What\'s the difference between SMS and RCS?',
                'answer' =>
                    'SMS is a basic text messaging service limited to 160 characters and no media, while RCS is an advanced messaging protocol that supports rich features like images, videos, read receipts, and typing indicators.',
            ],
            [
                'question' => 'Do SMS and RCS require the internet?',
                'answer' =>
                    'SMS does not need an internet connection—it uses the mobile network. RCS does require either mobile data or Wi-Fi to function.',
            ],
            [
                'question' => 'Are SMS and RCS available on all phones?',
                'answer' =>
                    'SMS is universally supported on all mobile phones. RCS is available mostly on devices through supported apps like Google Messages.',
            ],
            [
                'question' => 'Are there costs associated with using SMS or RCS?',
                'answer' =>
                    'SMS may incur charges based on your mobile plan. RCS usually uses data, so it won\'t have per-message fees.',
            ],
            [
                'question' => 'What happens if RCS isn\'t available?',
                'answer' =>
                    'If RCS is not supported by one of the users\' devices or carriers, the message will automatically fall back to SMS or MMS, ensuring the message is still delivered—just without the rich features.',
            ],
        ];
    @endphp
    <section class=" overflow-hidden">
        <div class="container-fluid w-90 rounded-5 pb-lg-5 pb-md-5 pt-lg-5">
            <div class="container-lg ">
                <h3 class="text-center mb-4 fw-bold androsms-h2">Frequently Asked Questions</h3>
                <x-faq-accordion :faqs="$faqs" collapsed-color="#f9f9f9" active-color="#e13362" />
            </div>
        </div>
    </section>





    @include('components.andro.earn')
@endsection
